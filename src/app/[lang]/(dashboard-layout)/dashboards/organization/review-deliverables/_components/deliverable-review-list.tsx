"use client"

import { useState } from "react"
import Link from "next/link"
import { formatDistanceToNow } from "date-fns"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table"
import { Icons } from "@/components/icons"

interface DeliverableWithStudent {
  id: string
  title: string
  type: string
  status: string
  dueDate: Date
  submittedAt: Date | null
  student: {
    id: string
    name: string
    email: string
  }
  template: {
    id: string
    name: string
    type: string
    description: string
    daysFromStart: number
    instructions: string
    requiresAttachment: boolean | null
    maxAttachments: number | null
    requiresApproval: boolean | null
    isActive: boolean | null
    displayOrder: number
    createdAt: Date
    updatedAt: Date
    cycleId: string | null
  } | null
  revisions: Array<{
    id: string
    version: number
    content: string | null
    submittedAt: Date
    submittedBy: string | null
    feedback: string | null
    feedbackBy: string | null
    feedbackAt: Date | null
    status: string
    createdAt: Date
    deliverableId: string
    attachments: Array<{
      id: string
      name: string
      url: string
      size: number
      mimeType: string
      uploadedAt: string
    }>
  }>
  // Include all other deliverable fields
  [key: string]: any
}

interface DeliverableReviewListProps {
  deliverables: DeliverableWithStudent[]
  reviewerId: string
}

export function DeliverableReviewList({
  deliverables,
  reviewerId,
}: DeliverableReviewListProps) {
  const [filter, setFilter] = useState<"all" | "submitted" | "under_review">(
    "all"
  )

  const filteredDeliverables = deliverables.filter((deliverable) => {
    if (filter === "all") return true
    return deliverable.status === filter
  })

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "submitted":
        return <Badge variant="default">Submitted</Badge>
      case "under_review":
        return <Badge variant="secondary">Under Review</Badge>
      case "needs_revision":
        return <Badge variant="warning">Needs Revision</Badge>
      case "approved":
        return <Badge variant="success">Approved</Badge>
      default:
        return <Badge variant="outline">{status}</Badge>
    }
  }

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "learning_plan":
        return <Badge variant="outline">Learning Plan</Badge>
      case "midpoint_checkin":
        return <Badge variant="outline">Midpoint Check-in</Badge>
      case "final_reflection":
        return <Badge variant="outline">Final Reflection</Badge>
      default:
        return <Badge variant="outline">Custom</Badge>
    }
  }

  return (
    <div className="space-y-6">
      {/* Filters */}
      <Card>
        <CardHeader>
          <CardTitle>Filter Deliverables</CardTitle>
          <CardDescription>View deliverables by status</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button
              variant={filter === "all" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter("all")}
            >
              All ({deliverables.length})
            </Button>
            <Button
              variant={filter === "submitted" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter("submitted")}
            >
              Awaiting Review (
              {deliverables.filter((d) => d.status === "submitted").length})
            </Button>
            <Button
              variant={filter === "under_review" ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter("under_review")}
            >
              Under Review (
              {deliverables.filter((d) => d.status === "under_review").length})
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Deliverables Table */}
      <Card>
        <CardHeader>
          <CardTitle>Deliverables to Review</CardTitle>
          <CardDescription>
            Click on a deliverable to review and provide feedback
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Student</TableHead>
                <TableHead>Deliverable</TableHead>
                <TableHead>Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Submitted</TableHead>
                <TableHead>Version</TableHead>
                <TableHead className="text-right">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredDeliverables.map((deliverable) => (
                <TableRow key={deliverable.id}>
                  <TableCell>
                    <div>
                      <p className="font-medium">
                        {deliverable.student.name}
                      </p>
                      <p className="text-sm text-muted-foreground">
                        {deliverable.student.email}
                      </p>
                    </div>
                  </TableCell>
                  <TableCell className="font-medium">
                    {deliverable.title}
                  </TableCell>
                  <TableCell>{getTypeBadge(deliverable.type)}</TableCell>
                  <TableCell>{getStatusBadge(deliverable.status)}</TableCell>
                  <TableCell>
                    {deliverable.currentRevision ? (
                      <span className="text-sm">
                        {formatDistanceToNow(
                          new Date(deliverable.currentRevision.submittedAt),
                          {
                            addSuffix: true,
                          }
                        )}
                      </span>
                    ) : (
                      <span className="text-sm text-muted-foreground">
                        Not submitted
                      </span>
                    )}
                  </TableCell>
                  <TableCell>
                    {deliverable.currentRevision ? (
                      <span className="text-sm">
                        v{deliverable.currentRevision.version}
                      </span>
                    ) : (
                      <span className="text-sm text-muted-foreground">-</span>
                    )}
                  </TableCell>
                  <TableCell className="text-right">
                    <Button asChild size="sm">
                      <Link
                        href={`/dashboards/faculty/review-deliverables/${deliverable.id}`}
                      >
                        <Icons.Eye className="mr-2 h-4 w-4" />
                        Review
                      </Link>
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
              {filteredDeliverables.length === 0 && (
                <TableRow>
                  <TableCell
                    colSpan={7}
                    className="text-center text-muted-foreground"
                  >
                    No deliverables to review
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
