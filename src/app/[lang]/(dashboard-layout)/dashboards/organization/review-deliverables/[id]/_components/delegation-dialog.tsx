"use client"

import { useState, useTransition } from "react"

import { toast } from "@/hooks/use-toast"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Icons } from "@/components/icons"
import { delegateReview } from "../actions"

interface DelegationDialogProps {
  deliverableId: string
  delegatorId: string
  isOpen: boolean
  onClose: () => void
  onSuccess: () => void
}

export function DelegationDialog({
  deliverableId,
  delegatorId,
  isOpen,
  onClose,
  onSuccess,
}: DelegationDialogProps) {
  const [mentorEmail, setMentorEmail] = useState("")
  const [reason, setReason] = useState("")
  const [isPending, startTransition] = useTransition()

  const handleDelegate = () => {
    if (!mentorEmail.trim()) {
      toast({
        title: "Error",
        description: "Please enter the mentor's email address",
        variant: "destructive",
      })
      return
    }

    startTransition(async () => {
      try {
        const result = await delegateReview(
          deliverableId,
          delegatorId,
          mentorEmail,
          reason
        )

        if (result.success) {
          toast({
            title: "Success",
            description: "Review has been delegated successfully",
          })
          onSuccess()
          onClose()
          setMentorEmail("")
          setReason("")
        } else {
          throw new Error(result.error || "Failed to delegate review")
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error
              ? error.message
              : "Failed to delegate review",
          variant: "destructive",
        })
      }
    })
  }

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle>Delegate Review to Mentor</DialogTitle>
          <DialogDescription>
            Delegate the review of this deliverable to a mentor in your
            organization.
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4">
          <div className="grid gap-2">
            <Label htmlFor="mentor-email">Mentor Email</Label>
            <Input
              id="mentor-email"
              type="email"
              placeholder="<EMAIL>"
              value={mentorEmail}
              onChange={(e) => setMentorEmail(e.target.value)}
              disabled={isPending}
            />
          </div>
          <div className="grid gap-2">
            <Label htmlFor="reason">
              Reason for Delegation
              <span className="text-sm text-muted-foreground ml-2">
                (Optional)
              </span>
            </Label>
            <Textarea
              id="reason"
              placeholder="e.g., This mentor is more familiar with the student's project area"
              value={reason}
              onChange={(e) => setReason(e.target.value)}
              rows={3}
              disabled={isPending}
            />
          </div>
        </div>
        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={isPending}>
            Cancel
          </Button>
          <Button
            onClick={handleDelegate}
            disabled={isPending || !mentorEmail.trim()}
          >
            {isPending && (
              <Icons.Loader2 className="mr-2 h-4 w-4 animate-spin" />
            )}
            Delegate Review
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  )
}
