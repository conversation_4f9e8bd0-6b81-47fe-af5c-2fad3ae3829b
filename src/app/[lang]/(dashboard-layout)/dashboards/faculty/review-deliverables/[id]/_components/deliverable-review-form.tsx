"use client"

import { useState, useTransition } from "react"
import { useRouter } from "next/navigation"
import { formatDistanceToNow } from "date-fns"

import { db } from "@/drizzle/db"
import type {
  deliverableDelegations,
  deliverableRevisions,
  deliverableTemplates,
  deliverables,
  users,
} from "@/drizzle/schema"
import type { InferSelectModel } from "drizzle-orm"

import { toast } from "@/hooks/use-toast"
import { Alert, AlertDescription } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Textarea } from "@/components/ui/textarea"
import { Icons } from "@/components/icons"
import { reviewDeliverable } from "../actions"

// Define the user type for consistency
type User = Pick<
  InferSelectModel<typeof users>,
  "id" | "name" | "email"
>

// Define the deliverable type that matches our query result
type DeliverableQueryResult = InferSelectModel<typeof deliverables> & {
  template: InferSelectModel<typeof deliverableTemplates> | null
  student: User
  revisions: Array<
    InferSelectModel<typeof deliverableRevisions> & {
      submittedByUser: User | null
      feedbackByUser: User | null
    }
  >
  delegations: Array<
    InferSelectModel<typeof deliverableDelegations> & {
      delegatedFromUser: User | null
      delegatedToUser: User | null
    }
  >
}

// Use NonNullable to ensure we have the deliverable (since we check for null in the page)
type Deliverable = NonNullable<DeliverableQueryResult>

interface DeliverableReviewFormProps {
  deliverable: Deliverable
  reviewerId: string
}

export function DeliverableReviewForm({
  deliverable,
  reviewerId,
}: DeliverableReviewFormProps) {
  const router = useRouter()
  const [decision, setDecision] = useState<"approved" | "needs_revision">(
    "approved"
  )
  const [feedback, setFeedback] = useState("")
  const [isPending, startTransition] = useTransition()

  const currentRevision = deliverable.revisions[0]
  const isDelegated = deliverable.delegations.some(
    (d: any) => d.delegatedTo === reviewerId
  )

  const handleSubmitReview = () => {
    if (!feedback.trim()) {
      toast({
        title: "Error",
        description: "Please provide feedback for the student",
        variant: "destructive",
      })
      return
    }

    startTransition(async () => {
      try {
        const result = await reviewDeliverable(
          deliverable.id,
          reviewerId,
          decision,
          feedback
        )

        if (result.success) {
          toast({
            title: "Success",
            description:
              decision === "approved"
                ? "Deliverable approved successfully"
                : "Revision requested successfully",
          })
          router.push("/dashboards/faculty/review-deliverables")
        } else {
          throw new Error(result.error || "Failed to submit review")
        }
      } catch (error) {
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to submit review",
          variant: "destructive",
        })
      }
    })
  }

  if (!currentRevision) {
    return (
      <Card>
        <CardContent className="pt-6">
          <p className="text-center text-muted-foreground">
            This deliverable has not been submitted yet.
          </p>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      {/* Delegation Notice */}
      {isDelegated && (
        <Alert>
          <Icons.Info className="h-4 w-4" />
          <AlertDescription>
            You have been delegated to review this deliverable by{" "}
            {deliverable.delegations[0]?.delegatedFromUser?.name}
          </AlertDescription>
        </Alert>
      )}

      {/* Deliverable Information */}
      <Card>
        <CardHeader>
          <div className="flex items-center justify-between">
            <div>
              <CardTitle>{deliverable.title}</CardTitle>
              <CardDescription>
                {deliverable.template?.description || deliverable.description}
              </CardDescription>
            </div>
            <Badge variant="outline">{deliverable.type}</Badge>
          </div>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span className="font-medium">Student:</span>{" "}
              {deliverable.student.name}
            </div>
            <div>
              <span className="font-medium">Email:</span>{" "}
              {deliverable.student.email}
            </div>
            <div>
              <span className="font-medium">Due Date:</span>{" "}
              {new Date(deliverable.dueDate).toLocaleDateString()}
            </div>
            <div>
              <span className="font-medium">Submitted:</span>{" "}
              {formatDistanceToNow(new Date(currentRevision.submittedAt), {
                addSuffix: true,
              })}
            </div>
            <div>
              <span className="font-medium">Version:</span>{" "}
              {currentRevision.version}
            </div>
            <div>
              <span className="font-medium">Status:</span>{" "}
              <Badge
                variant={
                  deliverable.status === "approved"
                    ? "success"
                    : deliverable.status === "needs_revision"
                      ? "warning"
                      : "default"
                }
              >
                {deliverable.status}
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Submission Content */}
      <Card>
        <CardHeader>
          <CardTitle>Student Submission</CardTitle>
          <CardDescription>
            Review the content submitted by the student
          </CardDescription>
        </CardHeader>
        <CardContent>
          <Tabs defaultValue="content" className="w-full">
            <TabsList className="grid w-full grid-cols-3">
              <TabsTrigger value="content">Content</TabsTrigger>
              <TabsTrigger value="attachments">Attachments</TabsTrigger>
              <TabsTrigger value="history">Revision History</TabsTrigger>
            </TabsList>

            <TabsContent value="content" className="mt-4">
              <div
                className="prose prose-sm max-w-none"
                dangerouslySetInnerHTML={{
                  __html: currentRevision.content || "",
                }}
              />
            </TabsContent>

            <TabsContent value="attachments">
              <div className="space-y-2">
                {currentRevision.attachments &&
                currentRevision.attachments.length > 0 ? (
                  currentRevision.attachments.map((attachment: any, index: number) => (
                    <div
                      key={index}
                      className="flex items-center justify-between rounded-lg border p-3"
                    >
                      <div className="flex items-center gap-2">
                        <Icons.File className="h-4 w-4" />
                        <span className="text-sm">{attachment.name}</span>
                        <span className="text-xs text-muted-foreground">
                          ({(attachment.size / 1024 / 1024).toFixed(2)} MB)
                        </span>
                      </div>
                      <Button variant="ghost" size="sm" asChild>
                        <a
                          href={attachment.url}
                          target="_blank"
                          rel="noopener noreferrer"
                        >
                          <Icons.Download className="h-4 w-4" />
                        </a>
                      </Button>
                    </div>
                  ))
                ) : (
                  <p className="text-center text-sm text-muted-foreground">
                    No attachments submitted
                  </p>
                )}
              </div>
            </TabsContent>

            <TabsContent value="history">
              <div className="space-y-3">
                {deliverable.revisions.map((revision: any) => (
                  <div key={revision.id} className="rounded-lg border p-4">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium">
                        Version {revision.version}
                      </span>
                      <Badge
                        variant={
                          revision.status === "approved"
                            ? "success"
                            : revision.status === "needs_revision"
                              ? "warning"
                              : "default"
                        }
                      >
                        {revision.status}
                      </Badge>
                    </div>
                    <p className="text-sm text-muted-foreground">
                      Submitted{" "}
                      {formatDistanceToNow(new Date(revision.submittedAt), {
                        addSuffix: true,
                      })}
                    </p>
                    {revision.feedback && (
                      <div className="mt-3">
                        <p className="text-sm font-medium mb-1">Feedback:</p>
                        <p className="text-sm text-muted-foreground">
                          {revision.feedback}
                        </p>
                        {revision.feedbackByUser && (
                          <p className="text-xs text-muted-foreground mt-1">
                            — {revision.feedbackByUser.name}
                          </p>
                        )}
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>

      {/* Review Form */}
      {deliverable.status !== "approved" && (
        <Card>
          <CardHeader>
            <CardTitle>Submit Review</CardTitle>
            <CardDescription>
              Provide your feedback and decision for this deliverable
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              <div>
                <Label>Decision</Label>
                <RadioGroup
                  value={decision}
                  onValueChange={(value) =>
                    setDecision(value as "approved" | "needs_revision")
                  }
                  className="mt-2"
                >
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem value="approved" id="approved" />
                    <Label htmlFor="approved" className="cursor-pointer">
                      Approve - The deliverable meets all requirements
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <RadioGroupItem
                      value="needs_revision"
                      id="needs_revision"
                    />
                    <Label htmlFor="needs_revision" className="cursor-pointer">
                      Request Revision - The student needs to make changes
                    </Label>
                  </div>
                </RadioGroup>
              </div>

              <div>
                <Label htmlFor="feedback">
                  Feedback
                  <span className="text-sm text-muted-foreground ml-2">
                    (Required - provide constructive feedback to the student)
                  </span>
                </Label>
                <Textarea
                  id="feedback"
                  value={feedback}
                  onChange={(e) => setFeedback(e.target.value)}
                  placeholder="Provide detailed feedback for the student..."
                  rows={6}
                  className="mt-2"
                />
              </div>
            </div>

            <div className="flex justify-end gap-4">
              <Button
                variant="outline"
                onClick={() =>
                  router.push("/dashboards/faculty/review-deliverables")
                }
              >
                Cancel
              </Button>
              <Button
                onClick={handleSubmitReview}
                disabled={isPending || !feedback.trim()}
              >
                {isPending && (
                  <Icons.Loader2 className="mr-2 h-4 w-4 animate-spin" />
                )}
                Submit Review
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Already Approved */}
      {deliverable.status === "approved" && (
        <Card className="border-green-200 bg-green-50">
          <CardContent className="pt-6">
            <div className="flex items-center gap-2">
              <Icons.CheckCircle className="h-5 w-5 text-green-600" />
              <p className="text-green-800">
                This deliverable has been approved.
              </p>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
