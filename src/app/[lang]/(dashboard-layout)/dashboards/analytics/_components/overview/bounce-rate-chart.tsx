"use client"

import { <PERSON>tes<PERSON><PERSON><PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, XAxis } from "recharts"

import type { ChartConfig } from "@/components/ui/chart"
import type { ChartTooltipContentProps, ChartTooltipPayloadItem } from "@/components/ui/chart.types"
import type { OverviewType } from "../../types"

import { formatPercent } from "@/lib/utils"

import { useIsRtl } from "@/hooks/use-is-rtl"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

function ModifiedChartTooltipContent({
  payload,
  ...props
}: ChartTooltipContentProps) {
  if (!payload || payload.length === 0) return null

  const formattedPayload = payload.map((item) => ({
    ...item,
    value:
      item.value !== undefined ? formatPercent(Number(item.value)) : undefined,
  }))

  return <ChartTooltipContent {...(props as any)} payload={formattedPayload} />
}

const chartConfig = {
  value: {
    label: "Rate",
  },
} satisfies ChartConfig

export function BounceRateChart({
  data,
}: {
  data: OverviewType["bounceRate"]["perMonth"]
}) {
  const isRtl = useIsRtl()

  return (
    <ChartContainer
      config={chartConfig}
      className="h-32 w-full rounded-b-md overflow-hidden"
    >
      <LineChart
        accessibilityLayer
        data={data}
        margin={{
          left: 0,
          right: 0,
        }}
      >
        <CartesianGrid vertical={false} />
        <XAxis reversed={isRtl} dataKey="month" hide />
        <ChartTooltip
          cursor={false}
          content={<ModifiedChartTooltipContent />}
        />
        <Line
          dataKey="value"
          type="linear"
          stroke="hsl(var(--chart-3))"
          strokeWidth={2}
          dot={false}
        />
      </LineChart>
    </ChartContainer>
  )
}
