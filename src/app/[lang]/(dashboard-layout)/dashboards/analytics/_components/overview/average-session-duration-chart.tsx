"use client"

import { <PERSON>, <PERSON><PERSON><PERSON>, Cartesian<PERSON>, XAxi<PERSON> } from "recharts"

import type { ChartConfig } from "@/components/ui/chart"
import type { ChartTooltipContentProps, ChartTooltipPayloadItem } from "@/components/ui/chart.types"
import type { OverviewType } from "../../types"

import { formatDuration } from "@/lib/utils"

import { useIsRtl } from "@/hooks/use-is-rtl"
import { useRadius } from "@/hooks/use-radius"
import {
  ChartContainer,
  ChartTooltip,
  ChartTooltipContent,
} from "@/components/ui/chart"

function ModifiedChartTooltipContent({
  payload,
  ...props
}: ChartTooltipContentProps) {
  if (!payload || payload.length === 0) return null

  const formattedPayload = payload.map((item) => ({
    ...item,
    value:
      item.value !== undefined ? formatDuration(Number(item.value)) : undefined,
  }))

  return <ChartTooltipContent {...(props as any)} payload={formattedPayload} />
}

const chartConfig = {
  value: {
    label: "Duration",
  },
} satisfies ChartConfig

export function AverageSessionDurationChart({
  data,
}: {
  data: OverviewType["averageSessionDuration"]["perMonth"]
}) {
  const isRtl = useIsRtl()
  const radius = useRadius()

  return (
    <ChartContainer
      config={chartConfig}
      className="h-32 w-full rounded-b-md overflow-hidden"
    >
      <BarChart accessibilityLayer data={data}>
        <CartesianGrid vertical={false} />
        <ChartTooltip
          cursor={false}
          content={<ModifiedChartTooltipContent />}
        />
        <XAxis reversed={isRtl} dataKey="month" hide />
        <Bar dataKey="value" barSize={44} radius={radius} />
      </BarChart>
    </ChartContainer>
  )
}
