"use server"

import { revalidatePath } from "next/cache"

import { DeliverablesService } from "@/lib/services/deliverables-service"

export async function submitDeliverable(
  deliverableId: string,
  userId: string,
  content: string,
  attachments: Array<{
    id: string
    name: string
    url: string
    size: number
    mimeType: string
    uploadedAt?: string
  }>
) {
  try {
    // Add uploadedAt to attachments if missing
    const attachmentsWithUploadedAt = attachments.map(attachment => ({
      ...attachment,
      uploadedAt: attachment.uploadedAt || new Date().toISOString()
    }))

    const revision = await DeliverablesService.submitDeliverable(
      deliverableId,
      userId,
      content,
      attachmentsWithUploadedAt
    )

    revalidatePath("/dashboards/student/deliverables")
    revalidatePath(`/dashboards/student/deliverables/${deliverableId}`)

    return { success: true, revision }
  } catch (error) {
    console.error("Failed to submit deliverable:", error)
    return {
      success: false,
      error:
        error instanceof Error ? error.message : "Failed to submit deliverable",
    }
  }
}

export async function saveDraft(
  deliverableId: string,
  userId: string,
  content: string,
  attachments: Array<{
    id: string
    name: string
    url: string
    size: number
    mimeType: string
  }>
) {
  try {
    const draft = await DeliverablesService.saveDraft(
      deliverableId,
      userId,
      content,
      attachments
    )

    return { success: true, draft }
  } catch (error) {
    console.error("Failed to save draft:", error)
    return {
      success: false,
      error: error instanceof Error ? error.message : "Failed to save draft",
    }
  }
}
