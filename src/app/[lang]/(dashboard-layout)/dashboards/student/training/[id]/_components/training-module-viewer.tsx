"use client"

import { useEffect, useState, useTransition } from "react"
import { useRouter } from "next/navigation"

import type { UserTrainingData } from "@/lib/services/training-service"

import { toast } from "@/hooks/use-toast"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Icons } from "@/components/icons"

interface TrainingModuleViewerProps {
  moduleId: string
}

interface TrainingResponse {
  success: boolean
  data: UserTrainingData
  error?: string
}

export function TrainingModuleViewer({ moduleId }: TrainingModuleViewerProps) {
  const router = useRouter()
  const [data, setData] = useState<UserTrainingData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [isStarting, startStartTransition] = useTransition()
  const [isCompleting, startCompleteTransition] = useTransition()
  const [timeSpent, setTimeSpent] = useState(0)
  const [startTime, setStartTime] = useState<Date | null>(null)

  useEffect(() => {
    fetchTrainingData()
  }, [moduleId])

  // Track time spent on page
  useEffect(() => {
    if (data?.progress?.status === "in_progress") {
      setStartTime(new Date())
      const interval = setInterval(() => {
        setTimeSpent((prev) => prev + 1)
      }, 60000) // Update every minute

      return () => clearInterval(interval)
    }
  }, [data?.progress?.status])

  // Update progress periodically when training is in progress
  useEffect(() => {
    if (data?.progress?.status === "in_progress" && timeSpent > 0) {
      updateProgress()
    }
  }, [timeSpent])

  const fetchTrainingData = async () => {
    try {
      setLoading(true)
      const response = await fetch(`/api/student/training/${moduleId}`)
      const result: TrainingResponse = await response.json()

      if (!result.success) {
        throw new Error(result.error || "Failed to load training module")
      }

      setData(result.data)
    } catch (error) {
      console.error("Failed to fetch training data:", error)
      setError(
        error instanceof Error
          ? error.message
          : "Failed to load training module"
      )
    } finally {
      setLoading(false)
    }
  }

  const updateProgress = async () => {
    if (!data?.module) return

    try {
      // Simple progress calculation: assume 1% per minute spent
      const progressPercentage = Math.min(95, timeSpent) // Cap at 95% until completion

      await fetch(`/api/student/training/${moduleId}/progress`, {
        method: "PUT",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          progress: progressPercentage,
          timeSpentMinutes: timeSpent,
        }),
      })
    } catch (error) {
      console.error("Failed to update progress:", error)
    }
  }

  const handleStartTraining = async () => {
    startStartTransition(async () => {
      try {
        const response = await fetch(
          `/api/student/training/${moduleId}/start`,
          {
            method: "POST",
          }
        )
        const result = await response.json()

        if (!result.success) {
          throw new Error(result.error || "Failed to start training")
        }

        toast({
          title: "Training Started",
          description: "You have successfully started this training module",
        })

        // Refresh data
        await fetchTrainingData()
      } catch (error) {
        console.error("Failed to start training:", error)
        toast({
          title: "Error",
          description:
            error instanceof Error ? error.message : "Failed to start training",
          variant: "destructive",
        })
      }
    })
  }

  const handleCompleteTraining = async () => {
    startCompleteTransition(async () => {
      try {
        // For this implementation, we'll assume 100% score for completion
        // In a real system, this might come from an assessment
        const response = await fetch(
          `/api/student/training/${moduleId}/complete`,
          {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              score: 100,
            }),
          }
        )
        const result = await response.json()

        if (!result.success) {
          throw new Error(result.error || "Failed to complete training")
        }

        toast({
          title: result.completed ? "Training Completed!" : "Training Failed",
          description: result.message,
          variant: result.completed ? "default" : "destructive",
        })

        if (result.completed) {
          // Redirect back to training dashboard after completion
          setTimeout(() => {
            router.push("/dashboards/student/training")
          }, 2000)
        }

        // Refresh data
        await fetchTrainingData()
      } catch (error) {
        console.error("Failed to complete training:", error)
        toast({
          title: "Error",
          description:
            error instanceof Error
              ? error.message
              : "Failed to complete training",
          variant: "destructive",
        })
      }
    })
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 bg-muted rounded animate-pulse" />
        <div className="h-64 bg-muted rounded animate-pulse" />
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <Icons.AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!data) {
    return null
  }

  const { module, progress, isOverdue, canStart } = data
  const isCompleted = progress?.status === "completed"
  const isInProgress = progress?.status === "in_progress"
  const isFailed = progress?.status === "failed"
  const hasStarted = progress && progress.status !== "not_started"

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <Button
            variant="ghost"
            onClick={() => router.back()}
            className="mb-4"
          >
            <Icons.ArrowLeft className="h-4 w-4 mr-2" />
            Back to Training
          </Button>
          <h1 className="text-3xl font-bold tracking-tight">{module.title}</h1>
          <p className="text-muted-foreground">{module.description}</p>
        </div>
        <div className="flex items-center space-x-2">
          {module.isRequired && <Badge variant="destructive">Required</Badge>}
          {isOverdue && !isCompleted && (
            <Badge variant="destructive">Overdue</Badge>
          )}
          {isCompleted && <Badge variant="success">Completed</Badge>}
          {isFailed && <Badge variant="destructive">Failed</Badge>}
          {isInProgress && <Badge variant="secondary">In Progress</Badge>}
        </div>
      </div>

      {/* Alerts */}
      {isOverdue && !isCompleted && (
        <Alert variant="destructive">
          <Icons.AlertTriangle className="h-4 w-4" />
          <AlertTitle>Training Overdue</AlertTitle>
          <AlertDescription>
            This training was due on{" "}
            {new Date(module.validUntilDate!).toLocaleDateString()}. Please
            complete it immediately.
          </AlertDescription>
        </Alert>
      )}

      {isFailed && (
        <Alert variant="destructive">
          <Icons.X className="h-4 w-4" />
          <AlertTitle>Training Failed</AlertTitle>
          <AlertDescription>
            You did not meet the minimum score requirement. You can retry this
            training.
            {module.minimumScore &&
              ` Minimum score required: ${module.minimumScore}%`}
          </AlertDescription>
        </Alert>
      )}

      {isCompleted && (
        <Alert className="border-green-200 bg-green-50">
          <Icons.CheckCircle className="h-4 w-4 text-green-600" />
          <AlertTitle className="text-green-800">Training Completed</AlertTitle>
          <AlertDescription className="text-green-700">
            Congratulations! You have successfully completed this training
            module.
            {progress?.completedAt && (
              <>
                {" "}
                Completed on{" "}
                {new Date(progress.completedAt).toLocaleDateString()}.
              </>
            )}
          </AlertDescription>
        </Alert>
      )}

      {/* Progress Card */}
      {progress && (
        <Card>
          <CardHeader>
            <CardTitle>Your Progress</CardTitle>
            <CardDescription>Track your completion status</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex items-center justify-between text-sm mb-2">
                <span>Progress: {progress.progress}%</span>
                <span>
                  {progress.timeSpentMinutes} / {module.durationMinutes || "∞"}{" "}
                  minutes
                </span>
              </div>
              <Progress value={progress.progress} />
            </div>

            {progress.score !== null && (
              <div>
                <p className="text-sm">
                  <span className="font-medium">Score:</span> {progress.score}%
                  {module.minimumScore && ` (minimum: ${module.minimumScore}%)`}
                </p>
              </div>
            )}

            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Started:</span>{" "}
                {progress.startedAt
                  ? new Date(progress.startedAt).toLocaleDateString()
                  : "Not started"}
              </div>
              <div>
                <span className="font-medium">Last Accessed:</span>{" "}
                {progress.lastAccessedAt
                  ? new Date(progress.lastAccessedAt).toLocaleDateString()
                  : "Never"}
              </div>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Training Content */}
      <Card>
        <CardHeader>
          <CardTitle>Training Content</CardTitle>
          {module.durationMinutes && (
            <CardDescription>
              Estimated duration: {module.durationMinutes} minutes
            </CardDescription>
          )}
        </CardHeader>
        <CardContent className="space-y-4">
          {module.contentType === "external_url" && module.externalUrl ? (
            <div className="space-y-4">
              <p>
                This training is hosted externally. Click the link below to
                access the content:
              </p>
              <Button asChild>
                <a
                  href={module.externalUrl}
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Icons.ExternalLink className="h-4 w-4 mr-2" />
                  Open External Training
                </a>
              </Button>
            </div>
          ) : (
            <div className="prose prose-sm max-w-none">
              {module.content ? (
                <div dangerouslySetInnerHTML={{ __html: module.content }} />
              ) : (
                <p>Training content will be provided here.</p>
              )}
            </div>
          )}

          <Separator />

          {/* Action Buttons */}
          <div className="flex items-center justify-between">
            <div className="text-sm text-muted-foreground">
              {module.validUntilDate && (
                <p>
                  Due: {new Date(module.validUntilDate).toLocaleDateString()}
                </p>
              )}
            </div>

            <div className="flex items-center space-x-2">
              {isCompleted ? (
                <Button variant="outline" disabled>
                  <Icons.CheckCircle className="h-4 w-4 mr-2" />
                  Training Completed
                </Button>
              ) : !canStart ? (
                <Button disabled>
                  <Icons.Lock className="h-4 w-4 mr-2" />
                  Not Available Yet
                </Button>
              ) : !hasStarted ? (
                <Button onClick={handleStartTraining} disabled={isStarting}>
                  {isStarting && (
                    <Icons.Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  <Icons.Play className="h-4 w-4 mr-2" />
                  Start Training
                </Button>
              ) : (
                <Button
                  onClick={handleCompleteTraining}
                  disabled={isCompleting}
                >
                  {isCompleting && (
                    <Icons.Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  )}
                  <Icons.CheckCircle className="h-4 w-4 mr-2" />
                  Mark as Complete
                </Button>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Module Information */}
      <Card>
        <CardHeader>
          <CardTitle>Module Information</CardTitle>
        </CardHeader>
        <CardContent className="space-y-2 text-sm">
          <div className="grid grid-cols-2 gap-4">
            <div>
              <span className="font-medium">Content Type:</span>{" "}
              {module.contentType === "internal" && "Internal Content"}
              {module.contentType === "external_url" && "External Link"}
              {module.contentType === "video" && "Video Content"}
            </div>
            <div>
              <span className="font-medium">Required:</span>{" "}
              {module.isRequired ? "Yes" : "No"}
            </div>
            {module.minimumScore && (
              <div>
                <span className="font-medium">Minimum Score:</span>{" "}
                {module.minimumScore}%
              </div>
            )}
            {module.durationMinutes && (
              <div>
                <span className="font-medium">Duration:</span>{" "}
                {module.durationMinutes} minutes
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
