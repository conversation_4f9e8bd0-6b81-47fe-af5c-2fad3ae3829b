"use client"

import { useEffect, useState } from "react"
import Link from "next/link"

import type {
  TrainingProgressStats,
  UserTrainingData,
} from "@/lib/services/training-service"

import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import { Icons } from "@/components/icons"

interface TrainingResponse {
  success: boolean
  data: {
    training: UserTrainingData[]
    stats: TrainingProgressStats
  }
  error?: string
}

export function StudentTrainingDashboard() {
  const [data, setData] = useState<TrainingResponse["data"] | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    fetchTrainingData()
  }, [])

  const fetchTrainingData = async () => {
    try {
      setLoading(true)
      const response = await fetch("/api/student/training?includeStats=true")
      const result: TrainingResponse = await response.json()

      if (!result.success) {
        throw new Error(result.error || "Failed to load training data")
      }

      setData(result.data)
    } catch (error) {
      console.error("Failed to fetch training data:", error)
      setError(
        error instanceof Error ? error.message : "Failed to load training data"
      )
    } finally {
      setLoading(false)
    }
  }

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {Array.from({ length: 4 }).map((_, i) => (
            <Card key={i}>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="h-4 bg-muted rounded animate-pulse" />
                <div className="h-4 w-4 bg-muted rounded animate-pulse" />
              </CardHeader>
              <CardContent>
                <div className="h-8 bg-muted rounded animate-pulse mb-2" />
                <div className="h-3 bg-muted rounded animate-pulse" />
              </CardContent>
            </Card>
          ))}
        </div>
        <div className="h-64 bg-muted rounded animate-pulse" />
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <Icons.AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>{error}</AlertDescription>
      </Alert>
    )
  }

  if (!data) {
    return null
  }

  const { training, stats } = data
  const hasOverdueTraining = training.some(
    (t) => t.isOverdue && t.progress?.status !== "completed"
  )
  const hasIncompleteRequired = stats.completed < stats.totalRequired

  return (
    <div className="space-y-6">
      {/* Training Statistics */}
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Total Required
            </CardTitle>
            <Icons.BookOpen className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stats.totalRequired}</div>
            <p className="text-xs text-muted-foreground">
              Training modules to complete
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Completed</CardTitle>
            <Icons.CheckCircle className="h-4 w-4 text-green-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-green-600">
              {stats.completed}
            </div>
            <p className="text-xs text-muted-foreground">
              Successfully finished
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">In Progress</CardTitle>
            <Icons.Clock className="h-4 w-4 text-blue-600" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold text-blue-600">
              {stats.inProgress}
            </div>
            <p className="text-xs text-muted-foreground">
              Currently working on
            </p>
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              Completion Rate
            </CardTitle>
            <Icons.Target className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">
              {Math.round(stats.completionRate)}%
            </div>
            <Progress value={stats.completionRate} className="mt-2" />
          </CardContent>
        </Card>
      </div>

      {/* Alert for overdue or incomplete training */}
      {hasOverdueTraining && (
        <Alert variant="destructive">
          <Icons.AlertTriangle className="h-4 w-4" />
          <AlertTitle>Overdue Training</AlertTitle>
          <AlertDescription>
            You have overdue training modules that must be completed
            immediately. Failure to complete mandatory training may affect your
            eligibility for the program.
          </AlertDescription>
        </Alert>
      )}

      {hasIncompleteRequired && !hasOverdueTraining && (
        <Alert>
          <Icons.Info className="h-4 w-4" />
          <AlertTitle>Training Required</AlertTitle>
          <AlertDescription>
            You must complete all mandatory training modules before the deadline
            to participate in the SALL program.
          </AlertDescription>
        </Alert>
      )}

      {/* Training Modules List */}
      <Card>
        <CardHeader>
          <CardTitle>Training Modules</CardTitle>
          <CardDescription>
            Complete all required training modules to meet program requirements
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {training.length === 0 ? (
            <div className="text-center py-8">
              <Icons.CheckCircle className="h-12 w-12 text-green-600 mx-auto mb-4" />
              <h3 className="text-lg font-medium mb-2">
                All Training Complete!
              </h3>
              <p className="text-muted-foreground">
                You have successfully completed all required training modules.
              </p>
            </div>
          ) : (
            training.map((item, index) => {
              const { module, progress, isOverdue, canStart } = item
              const isCompleted = progress?.status === "completed"
              const isInProgress = progress?.status === "in_progress"
              const isFailed = progress?.status === "failed"
              const hasStarted = progress && progress.status !== "not_started"

              return (
                <div key={module.id}>
                  <div className="flex items-start space-x-4">
                    <div className="flex-shrink-0">
                      {isCompleted ? (
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-green-100">
                          <Icons.Check className="h-4 w-4 text-green-600" />
                        </div>
                      ) : isInProgress ? (
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-blue-100">
                          <Icons.Clock className="h-4 w-4 text-blue-600" />
                        </div>
                      ) : isFailed ? (
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-red-100">
                          <Icons.X className="h-4 w-4 text-red-600" />
                        </div>
                      ) : (
                        <div className="flex h-8 w-8 items-center justify-center rounded-full bg-muted">
                          <Icons.Circle className="h-4 w-4 text-muted-foreground" />
                        </div>
                      )}
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-center justify-between">
                        <div>
                          <h4 className="text-sm font-medium">
                            {module.title}
                          </h4>
                          <p className="text-sm text-muted-foreground">
                            {module.description}
                          </p>
                        </div>
                        <div className="flex items-center space-x-2">
                          {module.isRequired && (
                            <Badge variant="destructive">Required</Badge>
                          )}
                          {isOverdue && !isCompleted && (
                            <Badge variant="destructive">Overdue</Badge>
                          )}
                          {isCompleted && (
                            <Badge variant="success">Completed</Badge>
                          )}
                          {isFailed && (
                            <Badge variant="destructive">Failed</Badge>
                          )}
                          {isInProgress && (
                            <Badge variant="secondary">In Progress</Badge>
                          )}
                        </div>
                      </div>

                      {progress && (
                        <div className="mt-2 space-y-1">
                          <div className="flex items-center justify-between text-xs">
                            <span>Progress: {progress.progress}%</span>
                            {module.durationMinutes && (
                              <span>
                                {progress.timeSpentMinutes}/
                                {module.durationMinutes} minutes
                              </span>
                            )}
                          </div>
                          <Progress value={progress.progress} className="h-2" />
                          {progress.score && (
                            <p className="text-xs text-muted-foreground">
                              Score: {progress.score}%
                              {module.minimumScore &&
                                ` (minimum: ${module.minimumScore}%)`}
                            </p>
                          )}
                        </div>
                      )}

                      <div className="mt-3 flex items-center space-x-2">
                        {isCompleted ? (
                          <Button variant="outline" size="sm" asChild>
                            <Link
                              href={`/dashboards/student/training/${module.id}`}
                            >
                              <Icons.Eye className="h-4 w-4 mr-2" />
                              View Details
                            </Link>
                          </Button>
                        ) : !canStart ? (
                          <Button variant="outline" size="sm" disabled>
                            <Icons.Lock className="h-4 w-4 mr-2" />
                            Not Available Yet
                          </Button>
                        ) : (
                          <Button size="sm" asChild>
                            <Link
                              href={`/dashboards/student/training/${module.id}`}
                            >
                              {hasStarted ? (
                                <>
                                  <Icons.Play className="h-4 w-4 mr-2" />
                                  Continue
                                </>
                              ) : (
                                <>
                                  <Icons.Play className="h-4 w-4 mr-2" />
                                  Start Training
                                </>
                              )}
                            </Link>
                          </Button>
                        )}

                        {module.validUntilDate && (
                          <p className="text-xs text-muted-foreground">
                            Due:{" "}
                            {new Date(
                              module.validUntilDate
                            ).toLocaleDateString()}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>

                  {index < training.length - 1 && (
                    <Separator className="my-4" />
                  )}
                </div>
              )
            })
          )}
        </CardContent>
      </Card>

      {/* Application Eligibility Status */}
      {stats.totalRequired > 0 && (
        <Card>
          <CardHeader>
            <CardTitle>Application Eligibility</CardTitle>
            <CardDescription>
              Training completion status for SALL program eligibility
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="flex items-center space-x-2">
              {stats.completionRate === 100 ? (
                <>
                  <Icons.CheckCircle className="h-5 w-5 text-green-600" />
                  <span className="text-green-600 font-medium">
                    You are eligible to submit your application
                  </span>
                </>
              ) : (
                <>
                  <Icons.Clock className="h-5 w-5 text-amber-600" />
                  <span className="text-amber-600 font-medium">
                    Complete all training to become eligible for application
                    submission
                  </span>
                </>
              )}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  )
}
