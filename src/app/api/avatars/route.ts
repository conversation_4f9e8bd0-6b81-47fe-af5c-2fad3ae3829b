import { NextResponse } from "next/server"

import type { NextRequest } from "next/server"

/**
 * Generate deterministic avatars based on a seed
 * This creates a simple SVG avatar with initials or a colored background
 */
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url)
    const seed = searchParams.get("seed") || "default"
    const size = parseInt(searchParams.get("size") || "40")

    // Generate a consistent color based on the seed
    const colors = [
      "#FF6B6B",
      "#4ECDC4",
      "#45B7D1",
      "#96CEB4",
      "#FECA57",
      "#48DBFB",
      "#0ABDE3",
      "#006BA6",
      "#EE5A24",
      "#009432",
      "#C44569",
      "#F8B500",
      "#786FA6",
      "#F5CD79",
      "#EA8685",
    ]

    // Simple hash function to get a consistent index
    let hash = 0
    for (let i = 0; i < seed.length; i++) {
      hash = seed.charCodeAt(i) + ((hash << 5) - hash)
    }
    const colorIndex = Math.abs(hash) % colors.length
    const backgroundColor = colors[colorIndex]

    // Extract initials from email or use first two characters of seed
    let initials = "?"
    if (seed.includes("@")) {
      // It's an email
      const namePart = seed.split("@")[0]
      const parts = namePart.split(/[._-]/)
      if (parts.length >= 2) {
        initials = (parts[0][0] + parts[1][0]).toUpperCase()
      } else {
        initials = namePart.substring(0, 2).toUpperCase()
      }
    } else {
      // Use first two characters
      initials = seed.substring(0, 2).toUpperCase()
    }

    // Generate SVG
    const svg = `
      <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
        <rect width="${size}" height="${size}" fill="${backgroundColor}" rx="${size * 0.1}"/>
        <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
              fill="white" font-family="system-ui, -apple-system, sans-serif" 
              font-size="${size * 0.4}" font-weight="600">
          ${initials}
        </text>
      </svg>
    `

    return new NextResponse(svg.trim(), {
      headers: {
        "Content-Type": "image/svg+xml",
        "Cache-Control": "public, max-age=31536000, immutable", // Cache for 1 year
      },
    })
  } catch (error) {
    console.error("Error generating avatar:", error)

    // Return a simple fallback SVG
    const fallbackSvg = `
      <svg width="40" height="40" viewBox="0 0 40 40" xmlns="http://www.w3.org/2000/svg">
        <rect width="40" height="40" fill="#6B7280" rx="4"/>
        <text x="50%" y="50%" dominant-baseline="middle" text-anchor="middle" 
              fill="white" font-family="system-ui, -apple-system, sans-serif" 
              font-size="16" font-weight="600">?</text>
      </svg>
    `

    return new NextResponse(fallbackSvg.trim(), {
      headers: {
        "Content-Type": "image/svg+xml",
        "Cache-Control": "public, max-age=3600", // Cache for 1 hour
      },
      status: 200,
    })
  }
}
