import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import {
  files,
  studentApplications,
  studentAreaOfLawRankings,
  studentGrades,
  studentProfiles,
  studentResearchInterests,
  studentSelfProposedPlacements,
  studentStatements,
} from "@/drizzle/schema"
import { put } from "@vercel/blob"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"
import pdfParse from "pdf-parse"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

// Helper function to parse grades from transcript
async function parseTranscript(pdfBuffer: Buffer): Promise<{
  coreGPA: number | null
  lrwGPA: number | null
  overallGPA: number | null
  grades: Array<{ courseCode: string; courseName: string; grade: string }>
}> {
  try {
    const data = await pdfParse(pdfBuffer)
    const text = data.text

    // Grade scale mapping (A+ = 5.0, decreasing by 0.5)
    const gradeScale: Record<string, number> = {
      "A+": 5.0,
      A: 4.5,
      "A-": 4.0,
      "B+": 3.5,
      B: 3.0,
      "B-": 2.5,
      "C+": 2.0,
      C: 1.5,
      "C-": 1.0,
      "D+": 0.5,
      D: 0.0,
      F: 0.0,
    }

    // Parse individual grades
    const gradePattern =
      /([A-Z]{2,4}\s*\d{4}[A-Z]?)\s+(.+?)\s+([ABCDF][+-]?)\s*$/gm
    const grades: Array<{
      courseCode: string
      courseName: string
      grade: string
    }> = []
    let match

    while ((match = gradePattern.exec(text)) !== null) {
      grades.push({
        courseCode: match[1].trim(),
        courseName: match[2].trim(),
        grade: match[3].trim(),
      })
    }

    // Calculate GPAs
    const coreGrades = grades.filter((g) => !g.courseCode.includes("LRW"))
    const lrwGrades = grades.filter((g) => g.courseCode.includes("LRW"))

    const calculateGPA = (gradeList: typeof grades) => {
      if (gradeList.length === 0) return null
      const total = gradeList.reduce(
        (sum, g) => sum + (gradeScale[g.grade] || 0),
        0
      )
      return Number((total / gradeList.length).toFixed(2))
    }

    const coreGPA = calculateGPA(coreGrades)
    const lrwGPA = calculateGPA(lrwGrades)
    const overallGPA = calculateGPA(grades)

    return { coreGPA, lrwGPA, overallGPA, grades }
  } catch (error) {
    console.error("Error parsing transcript:", error)
    return { coreGPA: null, lrwGPA: null, overallGPA: null, grades: [] }
  }
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session || session.user.role !== "student") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const formData = await request.formData()

    // Extract form fields
    const studentId = formData.get("studentId") as string
    const _email = formData.get("email") as string
    const streams = JSON.parse(formData.get("streams") as string)
    const geographicPreferences = JSON.parse(
      formData.get("geographicPreferences") as string
    )
    const workArrangement = formData.get("workArrangement") as string

    // File uploads
    const cvFile = formData.get("cv") as File | null
    const coverLetterFile = formData.get("coverLetter") as File | null
    const transcriptFile = formData.get("transcript") as File | null

    if (!cvFile || !transcriptFile) {
      return NextResponse.json(
        { error: "CV and transcript are required" },
        { status: 400 }
      )
    }

    // Check if student has already applied
    const existingProfile = await db.query.studentProfiles.findFirst({
      where: eq(studentProfiles.userId, session.user.id),
    })

    if (existingProfile?.hasApplied) {
      return NextResponse.json(
        { error: "You have already submitted an application" },
        { status: 400 }
      )
    }

    // Upload files to S3/Blob storage
    const uploadFile = async (file: File, prefix: string) => {
      const buffer = Buffer.from(await file.arrayBuffer())
      const filename = `${prefix}/${session.user.id}/${Date.now()}-${file.name}`

      const blob = await put(filename, buffer, {
        access: "public",
        contentType: file.type,
      })

      // Save file record to database
      const [fileRecord] = await db
        .insert(files)
        .values({
          originalName: file.name,
          filename: file.name,
          url: blob.url,
          size: file.size,
          mimeType: file.type,
          uploadedBy: session.user.id,
        })
        .returning()

      return fileRecord
    }

    // Upload files
    const cvRecord = await uploadFile(cvFile, "applications/cv")
    const coverLetterRecord = coverLetterFile
      ? await uploadFile(coverLetterFile, "applications/cover-letter")
      : null
    const transcriptRecord = await uploadFile(
      transcriptFile,
      "applications/transcript"
    )

    // Parse transcript for grades
    const transcriptBuffer = Buffer.from(await transcriptFile.arrayBuffer())
    const { coreGPA, lrwGPA, overallGPA, grades } =
      await parseTranscript(transcriptBuffer)

    // Start transaction to save all application data
    await db.transaction(async (tx) => {
      // Create or update student profile
      if (existingProfile) {
        await tx
          .update(studentProfiles)
          .set({
            studentId,
            hasApplied: true,
            applicationDate: new Date(),
            resumeFileId: cvRecord.id,
            coverLetterFileId: coverLetterRecord?.id,
            transcriptFileId: transcriptRecord.id,
            coreGPA: coreGPA?.toString() || null,
            lrwGPA: lrwGPA?.toString() || null,
            overallGPA: overallGPA?.toString() || null,
            geographicPreferences,
            workArrangementPreference: workArrangement,
            placementTypes: streams,
            updatedAt: new Date(),
          })
          .where(eq(studentProfiles.userId, session.user.id))
      } else {
        await tx.insert(studentProfiles).values({
          userId: session.user.id,
          studentId,
          hasApplied: true,
          applicationDate: new Date(),
          resumeFileId: cvRecord.id,
          coverLetterFileId: coverLetterRecord?.id,
          transcriptFileId: transcriptRecord.id,
          coreGPA: coreGPA?.toString() || null,
          lrwGPA: lrwGPA?.toString() || null,
          overallGPA: overallGPA?.toString() || null,
          geographicPreferences,
          workArrangementPreference: workArrangement,
          placementTypes: streams,
        })
      }

      // Create main application record
      const [_application] = await tx
        .insert(studentApplications)
        .values({
          studentId: session.user.id,
          applicationDate: new Date(),
          status: "submitted",
          streams,
          cvFileId: cvRecord.id,
          coverLetterFileId: coverLetterRecord?.id,
          transcriptFileId: transcriptRecord.id,
          geographicPreferences,
          workArrangement,
        })
        .returning()

      // Save grades
      if (grades.length > 0) {
        await tx.insert(studentGrades).values(
          grades.map((g) => ({
            studentId: session.user.id,
            courseCode: g.courseCode,
            courseName: g.courseName,
            grade: g.grade,
            term: "2024-Fall", // Assuming Fall term
            year: 2024,
          }))
        )
      }

      // Handle externship stream data
      if (streams.externship) {
        const externshipAreas = JSON.parse(
          formData.get("externshipAreas") as string
        )
        const externshipAreaRankings = JSON.parse(
          formData.get("externshipAreaRankings") as string
        )
        const externshipStatements = JSON.parse(
          formData.get("externshipStatements") as string
        )

        // Save area rankings
        for (const areaId of externshipAreas) {
          await tx.insert(studentAreaOfLawRankings).values({
            studentId: session.user.id,
            areaOfLawId: areaId,
            ranking: externshipAreaRankings[areaId],
          })
        }

        // Save statements
        for (const [areaId, statement] of Object.entries(
          externshipStatements
        )) {
          if (statement) {
            await tx.insert(studentStatements).values({
              studentId: session.user.id,
              areaOfLawId: areaId,
              statement: statement as string,
              wordCount: (statement as string).split(/\s+/).length,
            })
          }
        }
      }

      // Handle research stream data
      if (streams.research) {
        const researchProjects = JSON.parse(
          formData.get("researchProjects") as string
        )

        for (const projectId of researchProjects) {
          await tx.insert(studentResearchInterests).values({
            studentId: session.user.id,
            facultyId: projectId,
          })
        }
      }

      // Handle self-proposed stream data
      if (streams.selfProposed) {
        const selfProposedData = {
          organizationName: formData.get("selfProposedOrganization") as string,
          areaOfLaw: formData.get("selfProposedAreaOfLaw") as string,
          supervisorName: formData.get("selfProposedSupervisorName") as string,
          supervisorTitle: formData.get(
            "selfProposedSupervisorTitle"
          ) as string,
          supervisorEmail: formData.get(
            "selfProposedSupervisorEmail"
          ) as string,
          supervisorPhone: formData.get(
            "selfProposedSupervisorPhone"
          ) as string,
          proposal: formData.get("selfProposedProposal") as string,
        }

        await tx.insert(studentSelfProposedPlacements).values({
          studentId: session.user.id,
          ...selfProposedData,
          status: "pending",
        })
      }
    })

    return NextResponse.json({
      success: true,
      message: "Application submitted successfully",
    })
  } catch (error) {
    console.error("Error submitting application:", error)
    return NextResponse.json(
      { error: "Failed to submit application" },
      { status: 500 }
    )
  }
}
