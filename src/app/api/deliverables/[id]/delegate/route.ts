import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { users } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { DeliverablesService } from "@/lib/services/deliverables-service"

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

interface DelegateRequest {
  delegateEmail: string
  reason?: string
}

/**
 * POST /api/deliverables/[id]/delegate
 * Delegate review authority to another user
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)

    if (
      !session?.user?.id ||
      !session.user.role ||
      !["faculty", "organization"].includes(session.user.role)
    ) {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    const { id } = await params

    const body: DelegateRequest = await request.json()
    const { delegateEmail, reason } = body

    if (!delegateEmail?.trim()) {
      return NextResponse.json(
        { error: "Delegate email is required" },
        { status: 400 }
      )
    }

    // Find the delegate user by email
    const delegateUser = await db.query.users.findFirst({
      where: eq(users.email, delegateEmail.toLowerCase()),
    })

    if (!delegateUser) {
      return NextResponse.json(
        { error: "User not found with that email" },
        { status: 404 }
      )
    }

    // Ensure the delegate is from the same organization if delegator is an organization
    if (session.user.role === "organization") {
      // Check if delegate has same organization domain
      if (!session.user.email || !delegateUser.email) {
        return NextResponse.json(
          { error: "Email addresses are required for organization delegation" },
          { status: 400 }
        )
      }
      const delegatorDomain = session.user.email.split("@")[1]
      const delegateDomain = delegateUser.email.split("@")[1]

      if (delegatorDomain !== delegateDomain) {
        return NextResponse.json(
          { error: "Delegate must be from the same organization" },
          { status: 400 }
        )
      }
    }

    const delegation = await DeliverablesService.delegateReview(
      id,
      session.user.id,
      delegateUser.id,
      reason
    )

    return NextResponse.json({
      success: true,
      delegation,
      message: "Review delegated successfully",
    })
  } catch (error) {
    console.error("Failed to delegate review:", error)
    return NextResponse.json(
      {
        error:
          error instanceof Error ? error.message : "Failed to delegate review",
      },
      { status: 500 }
    )
  }
}
