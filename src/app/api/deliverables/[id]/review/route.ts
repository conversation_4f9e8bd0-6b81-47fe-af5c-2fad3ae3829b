import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { DeliverablesService } from "@/lib/services/deliverables-service"

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

interface ReviewRequest {
  status: "approved" | "needs_revision"
  feedback: string
}

/**
 * POST /api/deliverables/[id]/review
 * Review a deliverable (faculty/organization/delegated mentor)
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)

    if (
      !session?.user?.id ||
      !session.user.role ||
      !["faculty", "organization"].includes(session.user.role)
    ) {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    const { id } = await params

    const body: ReviewRequest = await request.json()
    const { status, feedback } = body

    if (!feedback?.trim()) {
      return NextResponse.json(
        { error: "Feedback is required" },
        { status: 400 }
      )
    }

    if (!["approved", "needs_revision"].includes(status)) {
      return NextResponse.json({ error: "Invalid status" }, { status: 400 })
    }

    const result = await DeliverablesService.reviewDeliverable(
      id,
      session.user.id,
      status,
      feedback
    )

    return NextResponse.json({
      success: true,
      ...result,
      message:
        status === "approved"
          ? "Deliverable approved successfully"
          : "Revision requested successfully",
    })
  } catch (error) {
    console.error("Failed to review deliverable:", error)
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Failed to review deliverable",
      },
      { status: 500 }
    )
  }
}
