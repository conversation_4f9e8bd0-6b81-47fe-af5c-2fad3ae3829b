import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { DeliverablesService } from "@/lib/services/deliverables-service"

interface RouteParams {
  params: Promise<{
    id: string
  }>
}

interface SubmitRequest {
  content: string
  attachments?: Array<{
    id: string
    name: string
    url: string
    size: number
    mimeType: string
  }>
}

/**
 * POST /api/deliverables/[id]/submit
 * Submit a deliverable
 */
export async function POST(request: NextRequest, { params }: RouteParams) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "student") {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    const { id } = await params

    const body: SubmitRequest = await request.json()
    const { content, attachments = [] } = body

    if (!content?.trim()) {
      return NextResponse.json(
        { error: "Content is required" },
        { status: 400 }
      )
    }

    // Add uploadedAt timestamp to attachments
    const attachmentsWithTimestamp = attachments.map(attachment => ({
      ...attachment,
      uploadedAt: new Date().toISOString()
    }))

    const revision = await DeliverablesService.submitDeliverable(
      id,
      session.user.id,
      content,
      attachmentsWithTimestamp
    )

    return NextResponse.json({
      success: true,
      revision,
      message: "Deliverable submitted successfully",
    })
  } catch (error) {
    console.error("Failed to submit deliverable:", error)
    return NextResponse.json(
      {
        error:
          error instanceof Error
            ? error.message
            : "Failed to submit deliverable",
      },
      { status: 500 }
    )
  }
}
