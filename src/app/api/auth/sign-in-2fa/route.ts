import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { users } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { authenticator } from "otplib"

import { AuditService } from "@/lib/audit-service"

export async function POST(req: Request) {
  try {
    const body = await req.json()
    const { userId, code } = body

    if (!userId || !code) {
      return NextResponse.json(
        { message: "User ID and 2FA code are required" },
        { status: 400 }
      )
    }

    // Find user by ID
    const [user] = await db
      .select()
      .from(users)
      .where(eq(users.id, userId))
      .limit(1)

    if (!user) {
      return NextResponse.json({ message: "Invalid user" }, { status: 401 })
    }

    // Check if 2FA is enabled
    if (!user.totpEnabled || !user.totpSecret) {
      return NextResponse.json(
        { message: "2FA is not enabled for this user" },
        { status: 400 }
      )
    }

    // Verify the TOTP code
    const isValid = authenticator.verify({
      token: code,
      secret: user.totpSecret,
    })

    if (!isValid) {
      // Check backup codes if TOTP fails
      if (user.totpBackupCodes && Array.isArray(user.totpBackupCodes)) {
        const backupIndex = user.totpBackupCodes.indexOf(code)
        if (backupIndex !== -1) {
          // Remove used backup code
          const newBackupCodes = [...user.totpBackupCodes]
          newBackupCodes.splice(backupIndex, 1)

          await db
            .update(users)
            .set({
              totpBackupCodes: newBackupCodes,
              totpLastUsed: new Date(),
            })
            .where(eq(users.id, userId))

          // Log successful login with backup code
          await AuditService.logUserLogin(user.id, user.email || "")
          await AuditService.log(
            "user_2fa_backup_used",
            `User ${user.email} used a backup code`,
            {
              userId: user.id,
              userEmail: user.email || "",
              remainingCodes: newBackupCodes.length,
            }
          )
        } else {
          return NextResponse.json(
            { message: "Invalid 2FA code" },
            { status: 401 }
          )
        }
      } else {
        return NextResponse.json(
          { message: "Invalid 2FA code" },
          { status: 401 }
        )
      }
    } else {
      // Update last used time to prevent replay attacks
      await db
        .update(users)
        .set({ totpLastUsed: new Date() })
        .where(eq(users.id, userId))

      // Log successful login
      await AuditService.logUserLogin(user.id, user.email || "")
    }

    // Fetch additional user data
    let payloadUserData = {
      role: user.role,
      isVerified: true,
      isOnboarded: false,
    }

    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVER_URL || "http://localhost:3000"}/api/auth/user-status`,
        {
          method: "POST",
          headers: {
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            nextAuthId: user.id,
          }),
        }
      )

      if (response.ok) {
        const userData = await response.json()
        payloadUserData = {
          role: userData.role || user.role,
          isVerified: userData.isVerified,
          isOnboarded: userData.isOnboarded,
        }
      }
    } catch (error) {
      console.error("Error fetching user data:", error)
    }

    // Return user data for NextAuth
    return NextResponse.json(
      {
        id: user.id,
        name: user.name,
        email: user.email,
        avatar: user.avatar || null,
        status: user.status || "active",
        role: payloadUserData.role,
        isVerified: payloadUserData.isVerified,
        isOnboarded: payloadUserData.isOnboarded,
      },
      { status: 200 }
    )
  } catch (error) {
    console.error("Error in 2FA sign-in:", error)
    return NextResponse.json(
      { message: "Failed to complete 2FA sign-in" },
      { status: 500 }
    )
  }
}
