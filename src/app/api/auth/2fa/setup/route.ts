/**
 * API Route: Setup 2FA for authenticated user
 * Generates TOTP secret, QR code, and backup codes
 */

import { db } from "@/drizzle/db"
import { users } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { Result, err, ok } from "@/lib/result"
import {
  encryptBackupCodes,
  encryptTotpSecret,
  setupTwoFactor,
} from "@/lib/totp-service"

interface SetupTwoFactorResponse {
  readonly qrCodeUrl: string
  readonly backupCodes: readonly string[]
  readonly secret?: string // Only for testing/debugging in dev
}

/**
 * POST /api/auth/2fa/setup
 * Sets up 2FA for the authenticated user
 */
export async function POST(request: NextRequest): Promise<Response> {
  // Get authenticated user session
  const session = await getServerSession(authOptions)
  if (!session?.user?.id) {
    return Response.json({ error: "Authentication required" }, { status: 401 })
  }

  // Get user from database
  const userResult = await db.query.users.findFirst({
    where: eq(users.id, session.user.id),
    columns: {
      id: true,
      email: true,
      totpEnabled: true,
    },
  })

  if (!userResult) {
    return Response.json({ error: "User not found" }, { status: 404 })
  }

  if (userResult.totpEnabled) {
    return Response.json(
      { error: "2FA is already enabled for this account" },
      { status: 400 }
    )
  }

  if (!userResult.email) {
    return Response.json(
      { error: "User email is required for 2FA setup" },
      { status: 400 }
    )
  }

  // Generate 2FA setup data
  const setupResult = await setupTwoFactor(userResult.email)
  if (!setupResult.success) {
    return Response.json(
      { error: `Failed to setup 2FA: ${setupResult.error}` },
      { status: 500 }
    )
  }

  // Encrypt secret and backup codes for storage
  const encryptedSecretResult = encryptTotpSecret(setupResult.data.secret)
  if (!encryptedSecretResult.success) {
    return Response.json(
      { error: `Failed to encrypt secret: ${encryptedSecretResult.error}` },
      { status: 500 }
    )
  }

  const encryptedBackupCodesResult = encryptBackupCodes(
    setupResult.data.backupCodes
  )
  if (!encryptedBackupCodesResult.success) {
    return Response.json(
      {
        error: `Failed to encrypt backup codes: ${encryptedBackupCodesResult.error}`,
      },
      { status: 500 }
    )
  }

  // Store encrypted data in database (but don't enable 2FA yet)
  const updateResult = await db
    .update(users)
    .set({
      totpSecret: encryptedSecretResult.data,
      totpBackupCodes: setupResult.data.backupCodes, // Store as array for verification
      updatedAt: new Date(),
    })
    .where(eq(users.id, session.user.id))
    .returning({ id: users.id })
    .then(() => ok(true))
    .catch((error) => err(`Database update failed: ${error.message}`))

  if (!updateResult.success) {
    return Response.json({ error: updateResult.error }, { status: 500 })
  }

  // Return setup data to client
  const response: SetupTwoFactorResponse = {
    qrCodeUrl: setupResult.data.qrCodeUrl,
    backupCodes: setupResult.data.backupCodes,
    ...(process.env.NODE_ENV === "development" && {
      secret: setupResult.data.secret,
    }),
  }

  return Response.json(response, { status: 200 })
}
