import crypto from "crypto"

import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { users } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import { authOptions } from "@/configs/next-auth"
import { AuditService } from "@/lib/audit-service"

function generateBackupCodes(count = 8): string[] {
  const codes: string[] = []
  for (let i = 0; i < count; i++) {
    // Generate 8-character alphanumeric codes
    const code = crypto.randomBytes(4).toString("hex").toUpperCase()
    codes.push(code)
  }
  return codes
}

export async function POST() {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id) {
      return NextResponse.json({ message: "Unauthorized" }, { status: 401 })
    }

    // Check if 2FA is enabled
    const [user] = await db
      .select({
        totpEnabled: users.totpEnabled,
        email: users.email,
      })
      .from(users)
      .where(eq(users.id, session.user.id))
      .limit(1)

    if (!user || !user.totpEnabled) {
      return NextResponse.json(
        { message: "2FA is not enabled" },
        { status: 400 }
      )
    }

    // Generate new backup codes
    const backupCodes = generateBackupCodes()

    // Update user with new backup codes
    await db
      .update(users)
      .set({
        totpBackupCodes: backupCodes,
        updatedAt: new Date(),
      })
      .where(eq(users.id, session.user.id))

    // Log the action
    await AuditService.log(
      "user_2fa_backup_regenerated",
      `User regenerated 2FA backup codes`,
      {
        userId: session.user.id,
        userEmail: user.email || "",
      }
    )

    return NextResponse.json({
      backupCodes,
      message: "Backup codes regenerated successfully",
    })
  } catch (error) {
    console.error("Failed to regenerate backup codes:", error)
    return NextResponse.json(
      { message: "Failed to regenerate backup codes" },
      { status: 500 }
    )
  }
}
