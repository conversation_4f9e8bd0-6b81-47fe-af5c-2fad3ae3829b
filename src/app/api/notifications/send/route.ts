import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { sendNotification } from "@/lib/notification-service"

/**
 * Send a notification (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { to, templateName, data } = body

    if (!to || !templateName || !data) {
      return NextResponse.json(
        { error: "Missing required fields: to, templateName, data" },
        { status: 400 }
      )
    }

    const success = await sendNotification(to, templateName, data)

    if (!success) {
      return NextResponse.json(
        { error: "Failed to send notification" },
        { status: 500 }
      )
    }

    return NextResponse.json({
      success: true,
      message: "Notification sent successfully",
    })
  } catch (error) {
    console.error("Notification send error:", error)
    return NextResponse.json(
      { error: "Failed to send notification" },
      { status: 500 }
    )
  }
}
