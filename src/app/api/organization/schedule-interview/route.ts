import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import {
  interviews,
  organizationProfiles,
  studentProfiles,
  users,
} from "@/drizzle/schema"
import { and, eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

// ICS file generation helper
function generateICSFile(interview: {
  title: string
  description: string
  startTime: Date
  endTime: Date
  location: string
  organizerEmail: string
  attendeeEmail: string
}) {
  const {
    title,
    description,
    startTime,
    endTime,
    location,
    organizerEmail,
    attendeeEmail,
  } = interview

  // Format dates to ICS format (YYYYMMDDTHHMMSSZ)
  const formatDate = (date: Date) => {
    return date
      .toISOString()
      .replace(/[-:]/g, "")
      .replace(/\.\d{3}/, "")
  }

  const uid = `${Date.now()}-${Math.random().toString(36).substr(2, 9)}@shadowland.com`
  const now = new Date()

  const icsContent = `BEGIN:VCALENDAR
VERSION:2.0
PRODID:-//Shadowland//Interview Scheduler//EN
CALSCALE:GREGORIAN
METHOD:REQUEST
BEGIN:VEVENT
UID:${uid}
DTSTAMP:${formatDate(now)}
DTSTART:${formatDate(startTime)}
DTEND:${formatDate(endTime)}
SUMMARY:${title}
DESCRIPTION:${description.replace(/\n/g, "\\n")}
LOCATION:${location}
ORGANIZER;CN=Organization:mailto:${organizerEmail}
ATTENDEE;CUTYPE=INDIVIDUAL;ROLE=REQ-PARTICIPANT;PARTSTAT=NEEDS-ACTION;RSVP=TRUE;CN=Student:mailto:${attendeeEmail}
STATUS:CONFIRMED
SEQUENCE:0
BEGIN:VALARM
ACTION:DISPLAY
DESCRIPTION:Interview reminder
TRIGGER:-PT15M
END:VALARM
END:VEVENT
END:VCALENDAR`

  return icsContent
}

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== "organization") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const {
      studentId,
      scheduledDate,
      scheduledTime,
      duration,
      location,
      interviewType,
      notes,
      meetingLink,
    } = await request.json()

    // Validate required fields
    if (
      !studentId ||
      !scheduledDate ||
      !scheduledTime ||
      !duration ||
      !interviewType
    ) {
      return NextResponse.json(
        { error: "Missing required fields" },
        { status: 400 }
      )
    }

    // Get organization profile
    const orgProfile = await db.query.organizationProfiles.findFirst({
      where: eq(organizationProfiles.userId, session.user.id),
    })

    if (!orgProfile) {
      return NextResponse.json(
        { error: "Organization profile not found" },
        { status: 404 }
      )
    }

    // Get student details
    const student = await db.query.studentProfiles.findFirst({
      where: eq(studentProfiles.id, studentId),
      with: {
        user: true,
      },
    })

    if (!student) {
      return NextResponse.json({ error: "Student not found" }, { status: 404 })
    }

    // Create start and end times
    const [hours, minutes] = scheduledTime.split(":").map(Number)
    const startTime = new Date(scheduledDate)
    startTime.setHours(hours, minutes, 0, 0)

    const endTime = new Date(startTime)
    endTime.setMinutes(endTime.getMinutes() + parseInt(duration))

    // Create interview record
    const [interview] = await db
      .insert(interviews)
      .values({
        title: `Interview with ${session.user.name || session.user.organizationName || "Organization"}`,
        organizationId: session.user.id,
        studentId,
        scheduledDate: startTime,
        duration: parseInt(duration),
        meetingType: interviewType,
        location: location || "TBD",
        meetingLink,
        status: "scheduled",
        notes,
      })
      .returning()

    // Generate ICS file content
    const icsContent = generateICSFile({
      title: `Interview with ${session.user.name || session.user.organizationName || "Organization"}`,
      description: `Interview Type: ${interviewType}\n${notes ? `Notes: ${notes}` : ""}${meetingLink ? `\nMeeting Link: ${meetingLink}` : ""}`,
      startTime,
      endTime,
      location: location || meetingLink || "TBD",
      organizerEmail: session.user.email!,
      attendeeEmail: student.user.email || "",
    })

    // TODO: Send email with ICS attachment to student and organization

    return NextResponse.json({
      success: true,
      interview,
      icsContent,
      message: "Interview scheduled successfully",
    })
  } catch (error) {
    console.error("Error scheduling interview:", error)
    return NextResponse.json(
      { error: "Failed to schedule interview" },
      { status: 500 }
    )
  }
}

// GET method to retrieve organization's interviews
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== "organization") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const orgProfile = await db.query.organizationProfiles.findFirst({
      where: eq(organizationProfiles.userId, session.user.id),
    })

    if (!orgProfile) {
      return NextResponse.json(
        { error: "Organization profile not found" },
        { status: 404 }
      )
    }

    const orgInterviews = await db.query.interviews.findMany({
      where: eq(interviews.organizationId, orgProfile.id),
      with: {
        student: true,
      },
      orderBy: (interviews, { desc }) => [desc(interviews.scheduledDate)],
    })

    return NextResponse.json(orgInterviews)
  } catch (error) {
    console.error("Error fetching interviews:", error)
    return NextResponse.json(
      { error: "Failed to fetch interviews" },
      { status: 500 }
    )
  }
}
