import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { matches } from "@/drizzle/schema"
import { and, eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

/**
 * Handle organization's response to matched students (accept/reject)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "organization") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const body = await request.json()
    const { matchId, action, rejectionReason } = body

    // Validate required fields
    if (!matchId || !action || !["accept", "reject"].includes(action)) {
      return NextResponse.json(
        { error: "Invalid match ID or action" },
        { status: 400 }
      )
    }

    // Find the match
    const match = await db.query.matches.findFirst({
      where: and(
        eq(matches.id, matchId),
        eq(matches.organizationId, session.user.id)
      ),
    })

    if (!match) {
      return NextResponse.json({ error: "Match not found" }, { status: 404 })
    }

    // Update match status
    const [updatedMatch] = await db
      .update(matches)
      .set({
        status: action === "accept" ? "supervisor_accepted" : "supervisor_rejected",
        orgAccepted: action === "accept",
        rejectionReason:
          action === "reject" ? rejectionReason || "No reason provided" : null,
        rejectedBy: action === "reject" ? "organization" : null,
        updatedAt: new Date(),
      })
      .where(eq(matches.id, matchId))
      .returning()

    return NextResponse.json({
      success: true,
      match: updatedMatch,
    })
  } catch (error) {
    console.error("Match response error:", error)
    return NextResponse.json(
      { error: "Failed to process match response" },
      { status: 500 }
    )
  }
}
