import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { facultyProfiles, matches } from "@/drizzle/schema"
import { and, eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== "faculty") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const { matchId, accept, reason } = await request.json()

    if (!matchId || typeof accept !== "boolean") {
      return NextResponse.json(
        { error: "Match ID and accept decision are required" },
        { status: 400 }
      )
    }

    // Get faculty profile
    const facultyProfile = await db.query.facultyProfiles.findFirst({
      where: eq(facultyProfiles.userId, session.user.id),
    })

    if (!facultyProfile) {
      return NextResponse.json(
        { error: "Faculty profile not found" },
        { status: 404 }
      )
    }

    // Get the match to verify it belongs to this faculty
    const match = await db.query.matches.findFirst({
      where: and(
        eq(matches.id, matchId),
        eq(matches.facultyId, facultyProfile.userId),
        eq(matches.matchType, "faculty")
      ),
    })

    if (!match) {
      return NextResponse.json(
        { error: "Match not found or unauthorized" },
        { status: 404 }
      )
    }

    // Update the match with faculty's response
    const [updatedMatch] = await db
      .update(matches)
      .set({
        supervisorResponse: accept ? "accepted" : "rejected",
        status: accept
          ? match.studentResponse === "accepted"
            ? "confirmed"
            : "supervisor_accepted"
          : "supervisor_rejected",
        rejectionReason: !accept ? reason : null,
        updatedAt: new Date(),
      })
      .where(eq(matches.id, matchId))
      .returning()

    // TODO: Send notification to student about faculty's decision

    return NextResponse.json({
      success: true,
      match: updatedMatch,
      message: accept
        ? "Student match accepted successfully"
        : "Student match rejected",
    })
  } catch (error) {
    console.error("Error processing match response:", error)
    return NextResponse.json(
      { error: "Failed to process match response" },
      { status: 500 }
    )
  }
}
