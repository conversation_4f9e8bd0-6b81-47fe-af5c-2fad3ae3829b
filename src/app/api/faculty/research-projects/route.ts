import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { facultyProfiles, projects } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

export async function PUT(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== "faculty") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const data = await request.json()
    const {
      name,
      description,
      responsibilities,
      qualifications,
      expectedOutcomes,
      timeCommitment,
      startDate,
      endDate,
      maxStudents,
      status,
      facultyId,
    } = data

    // Verify the faculty profile exists
    const facultyProfile = await db.query.facultyProfiles.findFirst({
      where: eq(facultyProfiles.userId, facultyId || session.user.id),
    })

    if (!facultyProfile) {
      return NextResponse.json(
        { error: "Faculty profile not found" },
        { status: 404 }
      )
    }

    // Create the project
    const [newProject] = await db
      .insert(projects)
      .values({
        title: name,
        description,
        facultyId: session.user.id,
        status: status || "active",
        startDate: new Date(startDate),
        endDate: endDate ? new Date(endDate) : null,
        requirements: [], // Initialize empty requirements array
      })
      .returning()

    // Update faculty profile with research project information
    const currentProjects = facultyProfile.researchProjects || []
    const newResearchProject = {
      id: newProject.id,
      name,
      description,
      responsibilities,
      qualifications,
      expectedOutcomes,
      timeCommitment,
      maxStudents,
      status,
      startDate,
      endDate,
    }

    await db
      .update(facultyProfiles)
      .set({
        researchProjects: [...currentProjects, newResearchProject],
        updatedAt: new Date(),
      })
      .where(eq(facultyProfiles.id, facultyProfile.id))

    return NextResponse.json(newProject)
  } catch (error) {
    console.error("Error creating research project:", error)
    return NextResponse.json(
      { error: "Failed to create research project" },
      { status: 500 }
    )
  }
}

// GET method to retrieve faculty research projects
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user || session.user.role !== "faculty") {
      return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
    }

    const facultyProfile = await db.query.facultyProfiles.findFirst({
      where: eq(facultyProfiles.userId, session.user.id),
    })

    if (!facultyProfile) {
      return NextResponse.json(
        { error: "Faculty profile not found" },
        { status: 404 }
      )
    }

    return NextResponse.json(facultyProfile.researchProjects || [])
  } catch (error) {
    console.error("Error fetching research projects:", error)
    return NextResponse.json(
      { error: "Failed to fetch research projects" },
      { status: 500 }
    )
  }
}
