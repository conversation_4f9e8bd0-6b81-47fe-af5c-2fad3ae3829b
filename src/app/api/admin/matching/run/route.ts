import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import {
  matches,
  organizationProfiles,
  studentApplications,
  studentAreaOfLawRankings,
  studentProfiles,
} from "@/drizzle/schema"
import { and, eq, inArray, isNull, sql } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type {
  Organization,
  ScoringWeights,
  Student,
} from "@/lib/matching-algorithm/gale-shapley"
import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { runOrganizationMatching } from "@/lib/matching-algorithm/gale-shapley"

/**
 * Run the Gale-Shapley matching algorithm for organization-student matching
 * Uses organization-proposing variant with capacity constraints
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    // Get weights from request body (optional)
    const body = await request.json()
    const weights: ScoringWeights = body.weights || {
      areaPreference: 0.4,
      statementScore: 0.25,
      coreGPA: 0.25,
      lrwGPA: 0.1,
    }

    // Get all unmatched students who selected Externship stream
    const unmatchedStudents = await db.query.studentProfiles.findMany({
      where: and(
        eq(studentProfiles.matchStatus, "pending"),
        isNull(studentProfiles.matchedOrganizationId),
        sql`${studentProfiles.placementTypes} @> ARRAY['externship']::text[]`
      ),
      with: {
        user: true,
      },
    })

    if (unmatchedStudents.length === 0) {
      return NextResponse.json({
        success: true,
        message: "No unmatched students for externship stream",
        matchesCreated: 0,
        unmatchedStudents: 0,
      })
    }

    // Get student applications to get submission timestamps
    const studentIds = unmatchedStudents.map((s) => s.userId)
    const applications = await db.query.studentApplications.findMany({
      where: inArray(studentApplications.studentId, studentIds),
    })
    const applicationMap = new Map(
      applications.map((app) => [app.studentId, app.applicationDate])
    )

    // Get student area of law rankings
    const studentAreaRankings =
      await db.query.studentAreaOfLawRankings.findMany({
        where: inArray(studentAreaOfLawRankings.studentId, studentIds),
      })

    // Group rankings by student
    const rankingsByStudent = studentAreaRankings.reduce(
      (acc, ranking) => {
        if (!acc[ranking.studentId]) {
          acc[ranking.studentId] = []
        }
        acc[ranking.studentId].push(ranking)
        return acc
      },
      {} as Record<string, typeof studentAreaRankings>
    )

    // Get all organizations with their area of law
    const organizationProfiles = await db.query.organizationProfiles.findMany({
      with: {
        user: true,
      },
    })

    // Get current matches for each organization to calculate remaining capacity
    const currentOrgMatches = await db
      .select({
        organizationId: matches.organizationId,
        count: sql<number>`count(${matches.id})`.as("count"),
      })
      .from(matches)
      .where(
        and(
          eq(matches.matchType, "organization"),
          eq(matches.status, "confirmed")
        )
      )
      .groupBy(matches.organizationId)

    const orgMatchCounts = new Map(
      currentOrgMatches.map((m) => [m.organizationId!, m.count])
    )

    // Convert to algorithm format
    const students: Student[] = unmatchedStudents.map((s) => ({
      id: s.userId,
      profile: s,
      areaRankings: rankingsByStudent[s.userId] || [],
      researchInterests: [], // Not used for org matching
      applicationDate: applicationMap.get(s.userId) || new Date(),
    }))

    const organizations: Organization[] = organizationProfiles.map((o) => ({
      id: o.userId,
      profile: o,
      capacity: o.maxStudents || 3, // Default to 3 if not set
      currentMatches: [], // Will be populated from existing matches
    }))

    // Run the Gale-Shapley algorithm
    const matchResults = runOrganizationMatching(
      students,
      organizations,
      weights
    )

    // Save match results
    if (matchResults.length > 0) {
      const matchRecords = matchResults.map((result) => ({
        studentId: result.studentId,
        organizationId: result.organizationId!,
        matchType: "organization" as const,
        score: result.score,
        status: "pending" as const,
        studentResponse: "pending" as const,
        supervisorResponse: "pending" as const,
      }))

      await db.insert(matches).values(matchRecords)
    }

    return NextResponse.json({
      success: true,
      algorithm: "gale-shapley",
      weights,
      matchesCreated: matchResults.length,
      unmatchedStudents: students.length - matchResults.length,
      details: {
        totalStudents: students.length,
        totalOrganizations: organizations.length,
        averageScore:
          matchResults.length > 0
            ? Math.round(
                matchResults.reduce((sum, m) => sum + m.score, 0) /
                  matchResults.length
              )
            : 0,
      },
    })
  } catch (error) {
    console.error("Gale-Shapley matching algorithm error:", error)
    return NextResponse.json(
      { error: "Failed to run matching algorithm" },
      { status: 500 }
    )
  }
}
