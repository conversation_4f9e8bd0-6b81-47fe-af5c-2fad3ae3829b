import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import {
  facultyProfiles as facultyProfilesTable,
  matches,
  organizationProfiles,
  projectFaculty,
  projects,
  studentAreaOfLawRankings,
  studentProfiles,
  studentResearchInterests,
} from "@/drizzle/schema"
import { and, eq, inArray, isNull } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type {
  FacultyProject,
  Organization,
  Student,
} from "@/lib/matching-algorithm/gale-shapley"
import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import {
  calculateFacultyScore,
  calculateOrganizationScore,
} from "@/lib/matching-algorithm/gale-shapley"
import { notifyMatchResults } from "@/lib/notification-service"

interface PromoteRequest {
  studentId: string
  targetType: "organization" | "faculty"
  targetId: string // Organization ID or Faculty ID
  projectId?: string // Required for faculty matches
}

/**
 * Promote a waitlisted student to fill a vacancy
 * This is used when a match falls through (rejection by either party)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    const body: PromoteRequest = await request.json()
    const { studentId, targetType, targetId, projectId } = body

    // Verify student is actually waitlisted (unmatched)
    const student = await db.query.studentProfiles.findFirst({
      where: and(
        eq(studentProfiles.userId, studentId),
        eq(studentProfiles.matchStatus, "pending"),
        isNull(studentProfiles.matchedOrganizationId),
        isNull(studentProfiles.matchedFacultyId)
      ),
      with: {
        user: true,
      },
    })

    if (!student) {
      return NextResponse.json(
        { error: "Student not found or already matched" },
        { status: 404 }
      )
    }

    // Get student's preferences
    const areaRankings = await db.query.studentAreaOfLawRankings.findMany({
      where: eq(studentAreaOfLawRankings.studentId, studentId),
    })

    const researchInterests = await db.query.studentResearchInterests.findMany({
      where: eq(studentResearchInterests.studentId, studentId),
    })

    const studentData: Student = {
      id: studentId,
      profile: student,
      areaRankings,
      researchInterests,
      applicationDate: student.createdAt || new Date(),
    }

    let score = 0
    let matchData: {
      studentId: string
      matchType: "organization" | "faculty"
      status:
        | "pending"
        | "student_accepted"
        | "student_rejected"
        | "supervisor_accepted"
        | "supervisor_rejected"
        | "confirmed"
        | "cancelled"
      studentAccepted?: boolean
      organizationId?: string
      organizationAccepted?: boolean
      score?: number
      facultyId?: string
      projectId?: string
      facultyAccepted?: boolean
    } = {
      studentId,
      matchType: targetType,
      status: "pending" as const,
      studentAccepted: false,
      score: 0, // Will be calculated later
    }

    if (targetType === "organization") {
      // Verify organization exists and has capacity
      const org = await db.query.organizationProfiles.findFirst({
        where: eq(organizationProfiles.userId, targetId),
        with: {
          user: true,
        },
      })

      if (!org) {
        return NextResponse.json(
          { error: "Organization not found" },
          { status: 404 }
        )
      }

      // Check current matches
      const currentMatches = await db.query.matches.findMany({
        where: and(
          eq(matches.organizationId, targetId),
          eq(matches.status, "confirmed")
        ),
      })

      if (currentMatches.length >= (org.maxStudents || 3)) {
        return NextResponse.json(
          { error: "Organization is at capacity" },
          { status: 400 }
        )
      }

      // Calculate match score
      const orgData: Organization = {
        id: targetId,
        profile: org,
        capacity: org.maxStudents || 3,
        currentMatches: currentMatches.map((m) => m.studentId),
      }

      score = calculateOrganizationScore(studentData, orgData)

      matchData = {
        ...matchData,
        organizationId: targetId,
        organizationAccepted: false,
        score: Math.round(score),
      }
    } else {
      // Faculty match
      if (!projectId) {
        return NextResponse.json(
          { error: "Project ID required for faculty matches" },
          { status: 400 }
        )
      }

      // Get project and verify it exists
      const project = await db.query.projects.findFirst({
        where: eq(projects.id, projectId),
      })

      if (!project) {
        return NextResponse.json(
          { error: "Project not found" },
          { status: 404 }
        )
      }

      // Get project supervisors
      const supervisors = await db.query.projectFaculty.findMany({
        where: eq(projectFaculty.projectId, projectId),
      })

      const facultyProfilesList = await db.query.facultyProfiles.findMany({
        where: inArray(
          facultyProfilesTable.userId,
          supervisors.map((s) => s.facultyId)
        ),
        with: {
          user: true,
        },
      })

      // Check capacity for all supervisors
      let hasCapacity = false
      let primarySupervisor = null

      for (const supervisor of supervisors) {
        const faculty = facultyProfilesList.find(
          (f: (typeof facultyProfilesList)[0]) =>
            f.userId === supervisor.facultyId
        )
        if (!faculty) continue

        const currentMatches = await db.query.matches.findMany({
          where: and(
            eq(matches.facultyId, supervisor.facultyId),
            eq(matches.status, "confirmed")
          ),
        })

        if (currentMatches.length < (faculty.maxStudents || 5)) {
          hasCapacity = true
        }

        if (supervisor.isPrimary) {
          primarySupervisor = supervisor.facultyId
        }
      }

      if (!hasCapacity || !primarySupervisor) {
        return NextResponse.json(
          { error: "All faculty on this project are at capacity" },
          { status: 400 }
        )
      }

      // Calculate match score
      const projectData: FacultyProject = {
        id: projectId,
        project,
        supervisors: supervisors.map((s) => {
          const faculty = facultyProfilesList.find(
            (f: (typeof facultyProfilesList)[0]) => f.userId === s.facultyId
          )!
          return {
            facultyId: s.facultyId,
            faculty,
            isPrimary: s.isPrimary,
            capacity: faculty.maxStudents || 5,
            currentMatches: [],
          }
        }),
      }

      score = calculateFacultyScore(studentData, projectData)

      matchData = {
        ...matchData,
        facultyId: primarySupervisor,
        projectId,
        facultyAccepted: false,
        score: Math.round(score),
      }
    }

    // Create the match if score is acceptable
    if (score < 40) {
      return NextResponse.json(
        { error: "Match score too low for promotion" },
        { status: 400 }
      )
    }

    // Ensure score is set (TypeScript safety)
    if (!matchData.score) {
      matchData.score = Math.round(score)
    }

    // Create the match
    const [newMatch] = await db.insert(matches).values(matchData).returning()

    // Send notifications
    const matchWithRelations = await db.query.matches.findFirst({
      where: eq(matches.id, newMatch.id),
      with: {
        student: {
          with: {
            studentProfile: true,
          },
        },
        organization: {
          with: {
            organizationProfile: true,
          },
        },
        faculty: {
          with: {
            facultyProfile: true,
          },
        },
      },
    })

    if (matchWithRelations) {
      await notifyMatchResults(matchWithRelations)
    }

    return NextResponse.json({
      success: true,
      match: {
        id: newMatch.id,
        studentId: newMatch.studentId,
        targetType,
        targetId,
        projectId,
        score: newMatch.score,
        status: newMatch.status,
      },
      message: "Student successfully promoted from waitlist",
    })
  } catch (error) {
    console.error("Waitlist promotion error:", error)
    return NextResponse.json(
      { error: "Failed to promote from waitlist" },
      { status: 500 }
    )
  }
}
