import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import {
  facultyProfiles,
  matches,
  projectFaculty,
  projects,
  studentApplications,
  studentProfiles,
  studentResearchInterests,
} from "@/drizzle/schema"
import { and, eq, inArray, isNull, sql } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type {
  FacultyProject,
  ScoringWeights,
  Student,
} from "@/lib/matching-algorithm/gale-shapley"
import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { runFacultyMatching } from "@/lib/matching-algorithm/gale-shapley"

/**
 * Run the Gale-Shapley matching algorithm for faculty-student matching
 * Uses project-proposing variant with co-supervision support
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    // Get weights from request body (optional)
    const body = await request.json()
    const weights: ScoringWeights = body.weights || {
      areaPreference: 0.4,
      statementScore: 0.25,
      coreGPA: 0.25,
      lrwGPA: 0.1,
    }

    // Get all unmatched students who selected RA stream
    const unmatchedRAStudents = await db.query.studentProfiles.findMany({
      where: and(
        eq(studentProfiles.matchStatus, "pending"),
        isNull(studentProfiles.matchedFacultyId),
        sql`${studentProfiles.placementTypes} @> ARRAY['research_assistant']::text[]`
      ),
      with: {
        user: true,
      },
    })

    if (unmatchedRAStudents.length === 0) {
      return NextResponse.json({
        success: true,
        message: "No unmatched students for research assistant stream",
        matchesCreated: 0,
        unmatchedStudents: 0,
      })
    }

    // Get student applications to get submission timestamps
    const studentIds = unmatchedRAStudents.map((s) => s.userId)
    const applications = await db.query.studentApplications.findMany({
      where: inArray(studentApplications.studentId, studentIds),
    })
    const applicationMap = new Map(
      applications.map((app) => [app.studentId, app.applicationDate])
    )

    // Get student research interests
    const studentInterests = await db.query.studentResearchInterests.findMany({
      where: inArray(studentResearchInterests.studentId, studentIds),
    })

    // Group interests by student
    const interestsByStudent = studentInterests.reduce(
      (acc, interest) => {
        if (!acc[interest.studentId]) {
          acc[interest.studentId] = []
        }
        acc[interest.studentId].push(interest)
        return acc
      },
      {} as Record<string, typeof studentInterests>
    )

    // Get all active faculty projects
    const activeProjects = await db.query.projects.findMany({
      where: eq(projects.status, "active"),
    })

    // Get project-faculty relationships
    const projectFacultyRelations = await db.query.projectFaculty.findMany({
      where: inArray(
        projectFaculty.projectId,
        activeProjects.map((p) => p.id)
      ),
    })

    // Get faculty profiles
    const facultyIds = [
      ...new Set(projectFacultyRelations.map((pf) => pf.facultyId)),
    ]
    const facultyProfilesData = await db.query.facultyProfiles.findMany({
      where: and(
        inArray(facultyProfiles.userId, facultyIds),
        eq(facultyProfiles.canSuperviseStudents, true)
      ),
      with: {
        user: true,
      },
    })

    const facultyProfileMap = new Map(
      facultyProfilesData.map((f) => [f.userId, f])
    )

    // Count current assignments per faculty
    const currentAssignments = await db
      .select({
        facultyId: matches.facultyId,
        count: sql<number>`count(${matches.id})`.as("count"),
      })
      .from(matches)
      .where(
        and(eq(matches.matchType, "faculty"), eq(matches.status, "confirmed"))
      )
      .groupBy(matches.facultyId)

    const facultyMatchCounts = new Map(
      currentAssignments.map((a) => [a.facultyId!, a.count])
    )

    // Convert to algorithm format
    const students: Student[] = unmatchedRAStudents.map((s) => ({
      id: s.userId,
      profile: s,
      areaRankings: [], // Not used for faculty matching
      researchInterests: interestsByStudent[s.userId] || [],
      applicationDate: applicationMap.get(s.userId) || new Date(),
    }))

    const facultyProjects: FacultyProject[] = activeProjects
      .map((project) => {
        const projectSupervisors = projectFacultyRelations.filter(
          (pf) => pf.projectId === project.id
        )

        const supervisors = projectSupervisors
          .map((pf) => {
            const faculty = facultyProfileMap.get(pf.facultyId)
            if (!faculty) return null

            const currentMatches = facultyMatchCounts.get(pf.facultyId) || 0
            const maxCapacity = faculty.maxStudents || 5 // Max 5 per faculty
            const remainingCapacity = Math.max(0, maxCapacity - currentMatches)

            return {
              facultyId: pf.facultyId,
              faculty,
              isPrimary: pf.isPrimary,
              capacity: remainingCapacity,
              currentMatches: [],
            }
          })
          .filter((s) => s !== null) as FacultyProject["supervisors"]

        return {
          id: project.id,
          project,
          supervisors,
        }
      })
      .filter((fp) => fp.supervisors.length > 0) // Only include projects with valid supervisors

    // Run the Gale-Shapley algorithm
    const matchResults = runFacultyMatching(students, facultyProjects, weights)

    // Save match results
    if (matchResults.length > 0) {
      const matchRecords = matchResults.map((result) => ({
        studentId: result.studentId,
        facultyId: result.facultyId!,
        projectId: result.projectId!,
        matchType: "faculty" as const,
        score: result.score,
        status: "pending" as const,
        studentResponse: "pending" as const,
        supervisorResponse: "pending" as const,
      }))

      await db.insert(matches).values(matchRecords)
    }

    return NextResponse.json({
      success: true,
      algorithm: "gale-shapley",
      weights,
      matchesCreated: matchResults.length,
      unmatchedStudents: students.length - matchResults.length,
      details: {
        totalStudents: students.length,
        totalProjects: facultyProjects.length,
        totalFaculty: facultyIds.length,
        averageScore:
          matchResults.length > 0
            ? Math.round(
                matchResults.reduce((sum, m) => sum + m.score, 0) /
                  matchResults.length
              )
            : 0,
      },
    })
  } catch (error) {
    console.error("Gale-Shapley faculty matching error:", error)
    return NextResponse.json(
      { error: "Failed to run faculty matching algorithm" },
      { status: 500 }
    )
  }
}
