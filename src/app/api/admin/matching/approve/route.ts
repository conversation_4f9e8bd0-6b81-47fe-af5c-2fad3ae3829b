import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { cycles, matches, studentProfiles, users } from "@/drizzle/schema"
import { addDays } from "date-fns"
import { and, eq, inArray } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"
import { notifyMatchResults } from "@/lib/notification-service"
import { DeliverablesService } from "@/lib/services/deliverables-service"

interface ApprovalRequest {
  matchIds?: string[] // Specific match IDs to approve
  approveAll?: boolean // Approve all pending matches
  rejectIds?: string[] // Specific match IDs to reject
  placementStartDate?: string // ISO date string for placement start
  placementEndDate?: string // ISO date string for placement end
}

/**
 * Approve or reject matching results
 * Sends notifications to all parties when matches are approved
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authOptions)

    if (!session?.user?.id || session.user.role !== "admin") {
      return NextResponse.json({ error: "Unauthorised" }, { status: 401 })
    }

    const body: ApprovalRequest = await request.json()
    const {
      matchIds,
      approveAll,
      rejectIds,
      placementStartDate,
      placementEndDate,
    } = body

    let approvedCount = 0
    let rejectedCount = 0

    // Get current cycle if no placement dates provided
    let defaultStartDate = placementStartDate
      ? new Date(placementStartDate)
      : null
    let defaultEndDate = placementEndDate ? new Date(placementEndDate) : null

    if (!defaultStartDate) {
      const currentCycle = await db.query.cycles.findFirst({
        where: eq(cycles.isCurrent, true),
      })
      if (currentCycle) {
        defaultStartDate = currentCycle.startDate
        defaultEndDate = currentCycle.endDate
      }
    }

    // Handle approvals
    if (approveAll) {
      // Approve all pending matches
      const pendingMatches = await db.query.matches.findMany({
        where: eq(matches.status, "pending"),
        with: {
          student: {
            with: {
              studentProfile: true,
            },
          },
          organization: {
            with: {
              organizationProfile: true,
            },
          },
          faculty: {
            with: {
              facultyProfile: true,
            },
          },
        },
      })

      if (pendingMatches.length > 0) {
        // Update all pending matches to approved
        await db
          .update(matches)
          .set({
            status: "confirmed",
            approvedAt: new Date(),
            approvedBy: session.user.id,
            placementStartDate: defaultStartDate,
            placementEndDate: defaultEndDate,
          })
          .where(eq(matches.status, "pending"))

        approvedCount = pendingMatches.length

        // Send notifications and schedule deliverables for all approved matches
        for (const match of pendingMatches) {
          await notifyMatchResults(match)

          // Schedule deliverables if placement start date is available
          if (defaultStartDate) {
            const placementType = match.matchType as "organization" | "faculty"
            const supervisorId =
              match.matchType === "organization"
                ? match.organizationId!
                : match.facultyId!

            await DeliverablesService.scheduleDeliverablesForStudent(
              match.studentId,
              defaultStartDate,
              placementType,
              supervisorId
            )
          }
        }
      }
    } else if (matchIds && matchIds.length > 0) {
      // Approve specific matches
      const matchesToApprove = await db.query.matches.findMany({
        where: and(
          inArray(matches.id, matchIds),
          eq(matches.status, "pending")
        ),
        with: {
          student: {
            with: {
              studentProfile: true,
            },
          },
          organization: {
            with: {
              organizationProfile: true,
            },
          },
          faculty: {
            with: {
              facultyProfile: true,
            },
          },
        },
      })

      if (matchesToApprove.length > 0) {
        await db
          .update(matches)
          .set({
            status: "confirmed",
            approvedAt: new Date(),
            approvedBy: session.user.id,
            placementStartDate: defaultStartDate,
            placementEndDate: defaultEndDate,
          })
          .where(
            and(inArray(matches.id, matchIds), eq(matches.status, "pending"))
          )

        approvedCount = matchesToApprove.length

        // Send notifications and schedule deliverables
        for (const match of matchesToApprove) {
          await notifyMatchResults(match)

          // Schedule deliverables if placement start date is available
          if (defaultStartDate) {
            const placementType = match.matchType as "organization" | "faculty"
            const supervisorId =
              match.matchType === "organization"
                ? match.organizationId!
                : match.facultyId!

            await DeliverablesService.scheduleDeliverablesForStudent(
              match.studentId,
              defaultStartDate,
              placementType,
              supervisorId
            )
          }
        }
      }
    }

    // Handle rejections
    if (rejectIds && rejectIds.length > 0) {
      const matchesToReject = await db.query.matches.findMany({
        where: inArray(matches.id, rejectIds),
        with: {
          student: true,
        },
      })

      if (matchesToReject.length > 0) {
        // Delete rejected matches
        await db.delete(matches).where(inArray(matches.id, rejectIds))

        // Update student profiles back to pending
        const studentIds = matchesToReject.map((m) => m.studentId)
        await db
          .update(studentProfiles)
          .set({
            matchStatus: "pending",
            matchedOrganizationId: null,
            matchedFacultyId: null,
          })
          .where(inArray(studentProfiles.userId, studentIds))

        rejectedCount = matchesToReject.length
      }
    }

    return NextResponse.json({
      success: true,
      approved: approvedCount,
      rejected: rejectedCount,
      message: `Approved ${approvedCount} matches, rejected ${rejectedCount} matches`,
    })
  } catch (error) {
    console.error("Match approval error:", error)
    return NextResponse.json(
      { error: "Failed to approve matches" },
      { status: 500 }
    )
  }
}
