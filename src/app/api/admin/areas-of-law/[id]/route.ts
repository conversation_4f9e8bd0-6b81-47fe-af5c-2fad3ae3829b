import { NextResponse } from "next/server"
import { db } from "@/drizzle/db"
import { areasOfLaw } from "@/drizzle/schema"
import { eq } from "drizzle-orm"
import { getServerSession } from "next-auth"

import type { NextRequest } from "next/server"

import { authOptions } from "@/configs/next-auth"

export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const session = await getServerSession(authOptions)
  const { id } = await params

  if (!session || session.user.role !== "admin") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const body = await request.json()
  const { name, description, isActive } = body

  if (!name) {
    return NextResponse.json({ error: "Name is required" }, { status: 400 })
  }

  const [updatedArea] = await db
    .update(areasOfLaw)
    .set({
      name,
      description,
      isActive,
      updatedAt: new Date(),
    })
    .where(eq(areasOfLaw.id, id))
    .returning()

  if (!updatedArea) {
    return NextResponse.json({ error: "Area not found" }, { status: 404 })
  }

  return NextResponse.json(updatedArea)
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  const session = await getServerSession(authOptions)
  const { id } = await params

  if (!session || session.user.role !== "admin") {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 })
  }

  const [deletedArea] = await db
    .delete(areasOfLaw)
    .where(eq(areasOfLaw.id, id))
    .returning()

  if (!deletedArea) {
    return NextResponse.json({ error: "Area not found" }, { status: 404 })
  }

  return NextResponse.json({ message: "Area deleted successfully" })
}
