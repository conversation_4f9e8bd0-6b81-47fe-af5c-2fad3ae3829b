import { NextResponse } from "next/server"
import { getServerSession } from "next-auth"
import { z } from "zod"

import type { NextRequest } from "next/server"

import { authOptions as authConfig } from "@/configs/next-auth"
import { TrainingService } from "@/lib/services/training-service"

const createTrainingModuleSchema = z.object({
  title: z.string().min(1).max(255),
  description: z.string().min(1),
  content: z.string().optional(),
  contentType: z
    .enum(["internal", "external_url", "video"])
    .default("internal"),
  externalUrl: z.string().url().optional(),
  isRequired: z.boolean().default(false),
  minimumScore: z.number().min(0).max(100).optional(),
  durationMinutes: z.number().min(1).optional(),
  validFromDate: z.string().datetime().optional(),
  validUntilDate: z.string().datetime().optional(),
  targetRoles: z.array(z.string()).min(1),
  isActive: z.boolean().default(true),
  displayOrder: z.number().min(0).default(0),
})

/**
 * GET /api/admin/training
 * Get all training modules (admin only)
 */
export async function GET(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      )
    }

    const { searchParams } = new URL(request.url)
    const includeStats = searchParams.get("includeStats") === "true"

    // Get all training modules
    const modules = await TrainingService.getAllTrainingModules()

    // Optionally include system statistics
    let stats = null
    if (includeStats) {
      stats = await TrainingService.getSystemTrainingStats()
    }

    return NextResponse.json({
      success: true,
      data: {
        modules,
        stats,
      },
    })
  } catch (error) {
    console.error("Failed to get training modules:", error)
    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to get training modules",
      },
      { status: 500 }
    )
  }
}

/**
 * POST /api/admin/training
 * Create a new training module (admin only)
 */
export async function POST(request: NextRequest) {
  try {
    const session = await getServerSession(authConfig)

    if (!session?.user?.id) {
      return NextResponse.json(
        { error: "Authentication required" },
        { status: 401 }
      )
    }

    if (session.user.role !== "admin") {
      return NextResponse.json(
        { error: "Admin access required" },
        { status: 403 }
      )
    }

    const body = await request.json()
    const validatedData = createTrainingModuleSchema.parse(body)

    // Convert date strings to Date objects and ensure proper typing
    const moduleData = {
      ...validatedData,
      contentType: validatedData.contentType as string,
      validFromDate: validatedData.validFromDate
        ? new Date(validatedData.validFromDate)
        : null,
      validUntilDate: validatedData.validUntilDate
        ? new Date(validatedData.validUntilDate)
        : null,
    }

    if (!session.user.id) {
      return NextResponse.json(
        { error: "User ID is required" },
        { status: 400 }
      )
    }

    const module = await TrainingService.createTrainingModule(
      moduleData,
      session.user.id
    )

    return NextResponse.json({
      success: true,
      data: module,
      message: "Training module created successfully",
    })
  } catch (error) {
    console.error("Failed to create training module:", error)

    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          error: "Invalid data",
          details: error.errors,
        },
        { status: 400 }
      )
    }

    return NextResponse.json(
      {
        success: false,
        error:
          error instanceof Error
            ? error.message
            : "Failed to create training module",
      },
      { status: 500 }
    )
  }
}
