import { relations } from "drizzle-orm"
import {
  index,
  jsonb,
  pgEnum,
  pgTable,
  text,
  timestamp,
  uuid,
} from "drizzle-orm/pg-core"

import { users } from "./schema"

// Enum for audit log event types
export const auditEventTypeEnum = pgEnum("audit_event_type", [
  // User events
  "user_registered",
  "user_login",
  "user_logout",
  "user_updated",
  "user_deleted",
  "user_preferences_updated",
  "user_role_changed",
  "user_2fa_enabled",
  "user_2fa_disabled",
  "user_2fa_backup_used",
  "user_2fa_backup_regenerated",
  "user_password_reset",

  // Admin events
  "admin_impersonation_start",
  "admin_impersonation_end",
  "admin_user_created",
  "admin_user_updated",
  "admin_user_deleted",
  "admin_settings_updated",

  // Application events
  "application_submitted",
  "application_updated",
  "application_withdrawn",
  "application_approved",
  "application_rejected",

  // Matching events
  "matching_run",
  "match_approved",
  "match_rejected",
  "match_completed",
  "waitlist_promotion",

  // Organization events
  "organization_created",
  "organization_updated",
  "organization_joined",
  "organization_left",
  "project_created",
  "project_updated",
  "project_deleted",

  // Faculty events
  "faculty_profile_created",
  "faculty_profile_updated",
  "faculty_project_created",
  "faculty_project_updated",
  "faculty_project_deleted",

  // Deliverable events
  "deliverable_submitted",
  "deliverable_reviewed",
  "deliverable_approved",
  "deliverable_revision_requested",

  // Support events
  "ticket_created",
  "ticket_updated",
  "ticket_resolved",

  // Email events
  "email_sent",
  "email_template_created",
  "email_template_updated",
  "email_template_deleted",

  // File events
  "file_uploaded",
  "file_downloaded",
  "file_deleted",
  "file_scan_completed",

  // System events
  "system_error",
  "security_alert",
  "data_export",
  "bulk_operation",
])

export const auditLogs = pgTable(
  "audit_logs",
  {
    id: uuid("id").primaryKey().defaultRandom(),

    // Event information
    eventType: auditEventTypeEnum("event_type").notNull(),
    eventDescription: text("event_description").notNull(),

    // User information
    userId: uuid("user_id").references(() => users.id, {
      onDelete: "set null",
    }),
    userEmail: text("user_email"), // Store email in case user is deleted
    userRole: text("user_role"), // Store role at time of event

    // Impersonation tracking
    impersonatorId: uuid("impersonator_id").references(() => users.id, {
      onDelete: "set null",
    }),
    impersonatorEmail: text("impersonator_email"),

    // Request information
    ipAddress: text("ip_address"),
    userAgent: text("user_agent"),
    requestMethod: text("request_method"),
    requestPath: text("request_path"),

    // Additional context
    entityType: text("entity_type"), // e.g., "user", "application", "organization"
    entityId: uuid("entity_id"), // ID of the affected entity
    metadata: jsonb("metadata"), // Additional event-specific data

    // Timestamps
    createdAt: timestamp("created_at").defaultNow().notNull(),

    // Data retention - events older than 1 year should be archived/deleted
    expiresAt: timestamp("expires_at").notNull(), // Set to createdAt + 1 year
  },
  (table) => ({
    // Indexes for common queries
    userIdIdx: index("audit_logs_user_id_idx").on(table.userId),
    eventTypeIdx: index("audit_logs_event_type_idx").on(table.eventType),
    createdAtIdx: index("audit_logs_created_at_idx").on(table.createdAt),
    entityIdx: index("audit_logs_entity_idx").on(
      table.entityType,
      table.entityId
    ),
    impersonatorIdx: index("audit_logs_impersonator_idx").on(
      table.impersonatorId
    ),
  })
)

// Relations
export const auditLogsRelations = relations(auditLogs, ({ one }) => ({
  user: one(users, {
    fields: [auditLogs.userId],
    references: [users.id],
  }),
  impersonator: one(users, {
    fields: [auditLogs.impersonatorId],
    references: [users.id],
  }),
}))

// Type exports
export type AuditLog = typeof auditLogs.$inferSelect
export type NewAuditLog = typeof auditLogs.$inferInsert
export type AuditEventType = (typeof auditEventTypeEnum.enumValues)[number]
