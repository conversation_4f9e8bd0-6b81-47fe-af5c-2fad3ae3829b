import type { Result } from "@/lib/result"
import { ok, err } from "@/lib/result"
import { testRedisConnection } from "@/lib/redis-service"
import {
  initializeNotificationQueue,
  initializeNotificationWorker,
  initializeQueueEvents,
} from "@/lib/notification-queue"

/**
 * Initialize the complete notification system
 * Should be called when the application starts
 */
export async function initializeNotificationSystem(): Promise<
  Result<
    {
      queue: boolean
      worker: boolean
      events: boolean
      redis: boolean
    },
    Error
  >
> {
  console.log("Initializing notification system...")

  const results = {
    queue: false,
    worker: false,
    events: false,
    redis: false,
  }

  // Test Redis connection first
  const redisTest = await testRedisConnection()
  if (!redisTest.success) {
    return err(new Error(`Redis connection failed: ${redisTest.error.message}`))
  }

  console.log("✅ Redis connection successful")
  results.redis = true

  // Initialize notification queue
  const queueResult = initializeNotificationQueue()
  if (!queueResult.success) {
    return err(
      new Error(`Queue initialization failed: ${queueResult.error.message}`)
    )
  }

  console.log("✅ Notification queue initialized")
  results.queue = true

  // Initialize notification worker
  const workerResult = initializeNotificationWorker()
  if (!workerResult.success) {
    return err(
      new Error(`Worker initialization failed: ${workerResult.error.message}`)
    )
  }

  console.log("✅ Notification worker initialized")
  results.worker = true

  // Initialize queue events
  const eventsResult = initializeQueueEvents()
  if (!eventsResult.success) {
    return err(
      new Error(
        `Queue events initialization failed: ${eventsResult.error.message}`
      )
    )
  }

  console.log("✅ Queue events initialized")
  results.events = true

  console.log("🎉 Notification system fully initialized!")
  return ok(results)
}

/**
 * Health check for the notification system
 */
export async function checkNotificationSystemHealth(): Promise<
  Result<
    {
      status: "healthy" | "degraded" | "unhealthy"
      redis: { status: string; latency?: number }
      queue: { initialized: boolean; error?: string }
      worker: { initialized: boolean; error?: string }
      events: { initialized: boolean; error?: string }
    },
    Error
  >
> {
  const health = {
    status: "healthy" as const,
    redis: { status: "unknown" },
    queue: { initialized: false },
    worker: { initialized: false },
    events: { initialized: false },
  }

  let healthyComponents = 0
  const totalComponents = 4

  // Check Redis
  const startTime = Date.now()
  const redisTest = await testRedisConnection()
  const latency = Date.now() - startTime

  if (redisTest.success) {
    health.redis = { status: "connected", latency }
    healthyComponents++
  } else {
    health.redis = { status: `error: ${redisTest.error.message}` }
  }

  // Check queue
  const queueResult = initializeNotificationQueue()
  if (queueResult.success) {
    health.queue = { initialized: true }
    healthyComponents++
  } else {
    health.queue = { initialized: false, error: queueResult.error.message }
  }

  // Check worker
  const workerResult = initializeNotificationWorker()
  if (workerResult.success) {
    health.worker = { initialized: true }
    healthyComponents++
  } else {
    health.worker = { initialized: false, error: workerResult.error.message }
  }

  // Check events
  const eventsResult = initializeQueueEvents()
  if (eventsResult.success) {
    health.events = { initialized: true }
    healthyComponents++
  } else {
    health.events = { initialized: false, error: eventsResult.error.message }
  }

  // Determine overall health status
  if (healthyComponents === totalComponents) {
    health.status = "healthy"
  } else if (healthyComponents >= totalComponents / 2) {
    health.status = "degraded"
  } else {
    health.status = "unhealthy"
  }

  return ok(health)
}
