import { db } from "@/drizzle/db"
import { trainingModules, userTrainingProgress, users } from "@/drizzle/schema"
import { and, eq, gte, lte, sql } from "drizzle-orm"

import type { InferSelectModel } from "drizzle-orm"

export type TrainingModule = InferSelectModel<typeof trainingModules>
export type UserTrainingProgress = InferSelectModel<typeof userTrainingProgress>

export type TrainingStatus =
  | "not_started"
  | "in_progress"
  | "completed"
  | "failed"
  | "expired"

export interface TrainingProgressStats {
  totalRequired: number
  completed: number
  inProgress: number
  notStarted: number
  overdue: number
  completionRate: number
}

export interface UserTrainingData {
  module: TrainingModule
  progress: UserTrainingProgress | null
  isOverdue: boolean
  canStart: boolean
}

export class TrainingService {
  /**
   * Get all active training modules for a specific role
   */
  static async getTrainingModulesForRole(
    userRole: string
  ): Promise<TrainingModule[]> {
    return db.query.trainingModules.findMany({
      where: and(
        eq(trainingModules.isActive, true),
        sql`${trainingModules.targetRoles} @> ${JSON.stringify([userRole])}`
      ),
      orderBy: (modules, { asc }) => [
        asc(modules.displayOrder),
        asc(modules.title),
      ],
    })
  }

  /**
   * Get all required training modules for a user
   */
  static async getRequiredTrainingForUser(
    userId: string
  ): Promise<UserTrainingData[]> {
    // Get user role
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: { role: true },
    })

    if (!user) {
      throw new Error("User not found")
    }

    // Get all required training modules for this role
    const modules = await db.query.trainingModules.findMany({
      where: and(
        eq(trainingModules.isActive, true),
        eq(trainingModules.isRequired, true),
        sql`${trainingModules.targetRoles} @> ${JSON.stringify([user.role])}`
      ),
      orderBy: (modules, { asc }) => [
        asc(modules.displayOrder),
        asc(modules.title),
      ],
    })

    // Get user's progress for these modules
    const progressRecords = await db.query.userTrainingProgress.findMany({
      where: and(
        eq(userTrainingProgress.userId, userId),
        sql`${userTrainingProgress.trainingModuleId} = ANY(${modules.map((m) => m.id)})`
      ),
    })

    const progressMap = new Map(
      progressRecords.map((p) => [p.trainingModuleId, p])
    )

    // Combine module data with progress
    return modules.map((module) => {
      const progress = progressMap.get(module.id) || null
      const now = new Date()
      const isOverdue = module.validUntilDate
        ? now > module.validUntilDate
        : false
      const canStart = module.validFromDate ? now >= module.validFromDate : true

      return {
        module,
        progress,
        isOverdue,
        canStart,
      }
    })
  }

  /**
   * Get training progress statistics for a user
   */
  static async getUserTrainingStats(
    userId: string
  ): Promise<TrainingProgressStats> {
    const userTrainingData = await this.getRequiredTrainingForUser(userId)

    const totalRequired = userTrainingData.length
    const completed = userTrainingData.filter(
      (t) => t.progress?.status === "completed"
    ).length
    const inProgress = userTrainingData.filter(
      (t) => t.progress?.status === "in_progress"
    ).length
    const notStarted = userTrainingData.filter(
      (t) => !t.progress || t.progress.status === "not_started"
    ).length
    const overdue = userTrainingData.filter((t) => t.isOverdue).length

    const completionRate =
      totalRequired > 0 ? (completed / totalRequired) * 100 : 100

    return {
      totalRequired,
      completed,
      inProgress,
      notStarted,
      overdue,
      completionRate,
    }
  }

  /**
   * Check if user has completed all required training
   */
  static async hasUserCompletedRequiredTraining(
    userId: string
  ): Promise<boolean> {
    const stats = await this.getUserTrainingStats(userId)
    return stats.completionRate === 100
  }

  /**
   * Start training module for a user
   */
  static async startTraining(
    userId: string,
    trainingModuleId: string
  ): Promise<UserTrainingProgress> {
    // Check if module exists and user can access it
    const module = await db.query.trainingModules.findFirst({
      where: eq(trainingModules.id, trainingModuleId),
    })

    if (!module) {
      throw new Error("Training module not found")
    }

    if (!module.isActive) {
      throw new Error("Training module is not active")
    }

    // Check if user role is in target roles
    const user = await db.query.users.findFirst({
      where: eq(users.id, userId),
      columns: { role: true },
    })

    if (!user) {
      throw new Error("User not found")
    }

    const hasAccess = module.targetRoles.includes(user.role)
    if (!hasAccess) {
      throw new Error("User does not have access to this training module")
    }

    // Check validity dates
    const now = new Date()
    if (module.validFromDate && now < module.validFromDate) {
      throw new Error("Training module is not yet available")
    }

    if (module.validUntilDate && now > module.validUntilDate) {
      throw new Error("Training module has expired")
    }

    // Upsert progress record
    const [progress] = await db
      .insert(userTrainingProgress)
      .values({
        userId,
        trainingModuleId,
        status: "in_progress",
        progress: 0,
        startedAt: now,
        lastAccessedAt: now,
        attempts: 1,
      })
      .onConflictDoUpdate({
        target: [
          userTrainingProgress.userId,
          userTrainingProgress.trainingModuleId,
        ],
        set: {
          status: "in_progress",
          startedAt: sql`COALESCE(${userTrainingProgress.startedAt}, NOW())`,
          lastAccessedAt: now,
          attempts: sql`${userTrainingProgress.attempts} + 1`,
          updatedAt: now,
        },
      })
      .returning()

    return progress
  }

  /**
   * Update training progress
   */
  static async updateProgress(
    userId: string,
    trainingModuleId: string,
    progressPercentage: number,
    timeSpentMinutes: number = 0
  ): Promise<UserTrainingProgress> {
    const [progress] = await db
      .update(userTrainingProgress)
      .set({
        progress: Math.min(100, Math.max(0, progressPercentage)),
        lastAccessedAt: new Date(),
        timeSpentMinutes: sql`${userTrainingProgress.timeSpentMinutes} + ${timeSpentMinutes}`,
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(userTrainingProgress.userId, userId),
          eq(userTrainingProgress.trainingModuleId, trainingModuleId)
        )
      )
      .returning()

    if (!progress) {
      throw new Error("Training progress not found")
    }

    return progress
  }

  /**
   * Complete training module
   */
  static async completeTraining(
    userId: string,
    trainingModuleId: string,
    score?: number
  ): Promise<UserTrainingProgress> {
    // Get the module to check minimum score requirement
    const module = await db.query.trainingModules.findFirst({
      where: eq(trainingModules.id, trainingModuleId),
    })

    if (!module) {
      throw new Error("Training module not found")
    }

    // Check if score meets minimum requirement
    if (module.minimumScore && (!score || score < module.minimumScore)) {
      // Mark as failed and allow retry
      const [progress] = await db
        .update(userTrainingProgress)
        .set({
          status: "failed",
          score,
          lastAccessedAt: new Date(),
          updatedAt: new Date(),
        })
        .where(
          and(
            eq(userTrainingProgress.userId, userId),
            eq(userTrainingProgress.trainingModuleId, trainingModuleId)
          )
        )
        .returning()

      if (!progress) {
        throw new Error("Training progress not found")
      }

      return progress
    }

    // Mark as completed
    const [progress] = await db
      .update(userTrainingProgress)
      .set({
        status: "completed",
        progress: 100,
        score,
        completedAt: new Date(),
        lastAccessedAt: new Date(),
        certificateIssued: true, // Auto-issue certificate for required training
        updatedAt: new Date(),
      })
      .where(
        and(
          eq(userTrainingProgress.userId, userId),
          eq(userTrainingProgress.trainingModuleId, trainingModuleId)
        )
      )
      .returning()

    if (!progress) {
      throw new Error("Training progress not found")
    }

    return progress
  }

  /**
   * Get training module details with user progress
   */
  static async getTrainingModuleWithProgress(
    moduleId: string,
    userId: string
  ): Promise<UserTrainingData> {
    const module = await db.query.trainingModules.findFirst({
      where: eq(trainingModules.id, moduleId),
      with: {
        createdBy: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })

    if (!module) {
      throw new Error("Training module not found")
    }

    const progress = await db.query.userTrainingProgress.findFirst({
      where: and(
        eq(userTrainingProgress.userId, userId),
        eq(userTrainingProgress.trainingModuleId, moduleId)
      ),
    }) ?? null

    const now = new Date()
    const isOverdue = module.validUntilDate
      ? now > module.validUntilDate
      : false
    const canStart = module.validFromDate ? now >= module.validFromDate : true

    return {
      module,
      progress,
      isOverdue,
      canStart,
    }
  }

  /**
   * Admin: Get all training modules
   */
  static async getAllTrainingModules(): Promise<TrainingModule[]> {
    return db.query.trainingModules.findMany({
      orderBy: (modules, { asc }) => [
        asc(modules.displayOrder),
        asc(modules.title),
      ],
      with: {
        createdBy: {
          columns: {
            id: true,
            name: true,
            email: true,
          },
        },
      },
    })
  }

  /**
   * Admin: Create training module
   */
  static async createTrainingModule(
    data: Omit<TrainingModule, "id" | "createdBy" | "createdAt" | "updatedAt">,
    createdBy: string
  ): Promise<TrainingModule> {
    const [module] = await db
      .insert(trainingModules)
      .values({
        ...data,
        createdBy,
      })
      .returning()

    return module
  }

  /**
   * Admin: Update training module
   */
  static async updateTrainingModule(
    moduleId: string,
    data: Partial<
      Omit<TrainingModule, "id" | "createdBy" | "createdAt" | "updatedAt">
    >
  ): Promise<TrainingModule> {
    const [module] = await db
      .update(trainingModules)
      .set({
        ...data,
        updatedAt: new Date(),
      })
      .where(eq(trainingModules.id, moduleId))
      .returning()

    if (!module) {
      throw new Error("Training module not found")
    }

    return module
  }

  /**
   * Admin: Delete training module
   */
  static async deleteTrainingModule(moduleId: string): Promise<void> {
    // First check if any users have progress on this module
    const hasProgress = await db.query.userTrainingProgress.findFirst({
      where: eq(userTrainingProgress.trainingModuleId, moduleId),
    })

    if (hasProgress) {
      throw new Error(
        "Cannot delete training module with existing user progress. Deactivate it instead."
      )
    }

    await db.delete(trainingModules).where(eq(trainingModules.id, moduleId))
  }

  /**
   * Admin: Get training statistics across all users
   */
  static async getSystemTrainingStats(): Promise<{
    totalUsers: number
    usersWithCompleteTraining: number
    totalModules: number
    requiredModules: number
    overallCompletionRate: number
  }> {
    // Get total users
    const [totalUsersResult] = await db
      .select({ count: sql<number>`count(*)` })
      .from(users)

    const totalUsers = totalUsersResult.count

    // Get total modules
    const [totalModulesResult] = await db
      .select({ count: sql<number>`count(*)` })
      .from(trainingModules)
      .where(eq(trainingModules.isActive, true))

    const totalModules = totalModulesResult.count

    // Get required modules
    const [requiredModulesResult] = await db
      .select({ count: sql<number>`count(*)` })
      .from(trainingModules)
      .where(
        and(
          eq(trainingModules.isActive, true),
          eq(trainingModules.isRequired, true)
        )
      )

    const requiredModules = requiredModulesResult.count

    // Get users with complete training (simplified - assumes all users are students)
    const [completedUsersResult] = await db
      .select({
        count: sql<number>`count(DISTINCT ${userTrainingProgress.userId})`,
      })
      .from(userTrainingProgress)
      .innerJoin(
        trainingModules,
        eq(userTrainingProgress.trainingModuleId, trainingModules.id)
      )
      .where(
        and(
          eq(userTrainingProgress.status, "completed"),
          eq(trainingModules.isRequired, true),
          eq(trainingModules.isActive, true)
        )
      )
      .groupBy(userTrainingProgress.userId)
      .having(sql`count(*) = ${requiredModules}`)

    const usersWithCompleteTraining = completedUsersResult.count || 0

    const overallCompletionRate =
      totalUsers > 0 ? (usersWithCompleteTraining / totalUsers) * 100 : 0

    return {
      totalUsers,
      usersWithCompleteTraining,
      totalModules,
      requiredModules,
      overallCompletionRate,
    }
  }
}
