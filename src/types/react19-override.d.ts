// Type overrides for React 19 compatibility

import "react"

// Removed global HTML attribute overrides - using component-level typing instead
// This prevents conflicts with React's built-in types

// Radix UI ScrollArea component type overrides
declare module "@radix-ui/react-scroll-area" {
  import type {
    ComponentPropsWithoutRef,
    ForwardRefExoticComponent,
  } from "react"
  import { ElementRef } from "react"

  interface ScrollAreaRootProps extends ComponentPropsWithoutRef<"div"> {
    children?: React.ReactNode
    className?: string
  }

  interface ScrollAreaViewportProps extends ComponentPropsWithoutRef<"div"> {
    children?: React.ReactNode
    className?: string
  }

  export const Root: ForwardRefExoticComponent<ScrollAreaRootProps>
  export const Viewport: ForwardRefExoticComponent<ScrollAreaViewportProps>
}
