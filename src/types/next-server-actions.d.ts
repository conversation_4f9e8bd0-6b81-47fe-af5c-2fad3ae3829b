// Next.js Server Actions type declarations
// This file explicitly marks certain patterns as NOT being Server Actions

// Removed global React module override - using "use client" directives instead

// Declare that these specific props are not Server Actions
declare global {
  type ClientSideEventHandler<T = unknown> = (value: T) => void | Promise<void>

  interface ClientComponentProps {
    onOpenChange?: ClientSideEventHandler<boolean>
    onValueChange?: ClientSideEventHandler<string>
    onCheckedChange?: ClientSideEventHandler<boolean>
    onPressedChange?: ClientSideEventHandler<boolean>
    onSelect?: ClientSideEventHandler<unknown>
    onDateChange?: ClientSideEventHandler<Date | undefined>
    onClose?: ClientSideEventHandler<void>
    onOpen?: ClientSideEventHandler<void>
    onChange?: ClientSideEventHandler<React.ChangeEvent<HTMLElement>>
    onSubmit?: ClientSideEventHandler<React.FormEvent<HTMLFormElement>>
    onClick?: ClientSideEventHandler<React.MouseEvent<HTMLElement>>
    onFocus?: ClientSideEventHandler<React.FocusEvent<HTMLElement>>
    onBlur?: ClientSideEventHandler<React.FocusEvent<HTMLElement>>
  }
}

export {}
