"use client"

import * as React from "react"
import * as SheetPrimitive from "@radix-ui/react-dialog"
import { XIcon } from "lucide-react"

import { cn } from "@/lib/utils"

// Module augmentation for Sheet components
declare module "@radix-ui/react-dialog" {
  interface DialogOverlayProps {
    className?: string
  }
  interface DialogContentProps {
    className?: string
    children?: React.ReactNode
  }
  interface DialogTitleProps {
    className?: string
    children?: React.ReactNode
  }
  interface DialogDescriptionProps {
    className?: string
    children?: React.ReactNode
  }
}

const Sheet = React.forwardRef<
  React.ComponentRef<typeof SheetPrimitive.Root>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Root>
>(({ ...props }, ref) => {
  const RootComponent = SheetPrimitive.Root as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Root> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <RootComponent data-slot="sheet" ref={ref} {...props} />
})
Sheet.displayName = "Sheet"

const SheetTrigger = React.forwardRef<
  React.ComponentRef<typeof SheetPrimitive.Trigger>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Trigger>
>(({ ...props }, ref) => {
  const TriggerComponent = SheetPrimitive.Trigger as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Trigger> & {
      ref?: React.Ref<HTMLButtonElement>
    }
  >
  return <TriggerComponent data-slot="sheet-trigger" ref={ref} {...props} />
})
SheetTrigger.displayName = "SheetTrigger"

const SheetClose = React.forwardRef<
  React.ComponentRef<typeof SheetPrimitive.Close>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Close>
>(({ ...props }, ref) => {
  const CloseComponent = SheetPrimitive.Close as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Close> & {
      ref?: React.Ref<HTMLButtonElement>
    }
  >
  return <CloseComponent data-slot="sheet-close" ref={ref} {...props} />
})
SheetClose.displayName = "SheetClose"

const SheetPortal = React.forwardRef<
  React.ComponentRef<typeof SheetPrimitive.Portal>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Portal>
>(({ ...props }, ref) => {
  const PortalComponent = SheetPrimitive.Portal as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Portal> & {
      ref?: React.Ref<HTMLDivElement>
    }
  >
  return <PortalComponent data-slot="sheet-portal" ref={ref} {...props} />
})
SheetPortal.displayName = "SheetPortal"

const SheetOverlay = React.forwardRef<
  React.ComponentRef<typeof SheetPrimitive.Overlay>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  const OverlayComponent = SheetPrimitive.Overlay as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Overlay> & {
      className?: string
      ref?: React.Ref<HTMLDivElement>
    }
  >

  return (
    <OverlayComponent
      data-slot="sheet-overlay"
      ref={ref}
      className={cn(
        "data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",
        className
      )}
      {...props}
    />
  )
})
SheetOverlay.displayName = "SheetOverlay"

const SheetContent = React.forwardRef<
  React.ComponentRef<typeof SheetPrimitive.Content>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content> & {
    className?: string
    children?: React.ReactNode
    side?: "top" | "right" | "bottom" | "left" | "start" | "end"
  }
>(({ className, children, side = "right", ...props }, ref) => {
  // Map start/end to left/right based on text direction
  const mappedSide = side === "start" ? "left" : side === "end" ? "right" : side
  const ContentComponent = SheetPrimitive.Content as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Content> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLDivElement>
    }
  >

  const CloseComponent = SheetPrimitive.Close as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Close> & {
      className?: string
      children?: React.ReactNode
    }
  >

  return (
    <SheetPortal>
      <SheetOverlay />
      <ContentComponent
        data-slot="sheet-content"
        ref={ref}
        className={cn(
          "bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500",
          mappedSide === "right" &&
            "data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm",
          mappedSide === "left" &&
            "data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm",
          mappedSide === "top" &&
            "data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b",
          mappedSide === "bottom" &&
            "data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",
          className
        )}
        {...props}
      >
        {children}
        <CloseComponent className="ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none">
          <XIcon className="size-4" />
          <span className="sr-only">Close</span>
        </CloseComponent>
      </ContentComponent>
    </SheetPortal>
  )
})
SheetContent.displayName = "SheetContent"

const SheetHeader = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<"div"> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  return (
    <div
      data-slot="sheet-header"
      ref={ref}
      className={cn("flex flex-col gap-1.5 p-4", className)}
      {...props}
    />
  )
})
SheetHeader.displayName = "SheetHeader"

const SheetFooter = React.forwardRef<
  HTMLDivElement,
  React.ComponentPropsWithoutRef<"div"> & {
    className?: string
  }
>(({ className, ...props }, ref) => {
  return (
    <div
      data-slot="sheet-footer"
      ref={ref}
      className={cn("mt-auto flex flex-col gap-2 p-4", className)}
      {...props}
    />
  )
})
SheetFooter.displayName = "SheetFooter"

const SheetTitle = React.forwardRef<
  React.ComponentRef<typeof SheetPrimitive.Title>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title> & {
    className?: string
    children?: React.ReactNode
  }
>(({ className, ...props }, ref) => {
  const TitleComponent = SheetPrimitive.Title as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Title> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLHeadingElement>
    }
  >

  return (
    <TitleComponent
      data-slot="sheet-title"
      ref={ref}
      className={cn("text-foreground font-semibold", className)}
      {...props}
    />
  )
})
SheetTitle.displayName = "SheetTitle"

const SheetDescription = React.forwardRef<
  React.ComponentRef<typeof SheetPrimitive.Description>,
  React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description> & {
    className?: string
    children?: React.ReactNode
  }
>(({ className, ...props }, ref) => {
  const DescriptionComponent = SheetPrimitive.Description as React.ComponentType<
    React.ComponentPropsWithoutRef<typeof SheetPrimitive.Description> & {
      className?: string
      children?: React.ReactNode
      ref?: React.Ref<HTMLParagraphElement>
    }
  >

  return (
    <DescriptionComponent
      data-slot="sheet-description"
      ref={ref}
      className={cn("text-muted-foreground text-sm", className)}
      {...props}
    />
  )
})
SheetDescription.displayName = "SheetDescription"

export {
  Sheet,
  SheetTrigger,
  SheetClose,
  SheetPortal,
  SheetContent,
  SheetHeader,
  SheetFooter,
  SheetTitle,
  SheetDescription,
}
